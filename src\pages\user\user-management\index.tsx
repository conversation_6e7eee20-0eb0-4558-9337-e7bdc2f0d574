import {
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  FilterFilled,
  KeyOutlined,
  PlusOutlined,
  ReloadOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { SubHeader } from '@layouts/admin';
import { EStatusState, QueryParams } from '@models';
import { InputSearch } from '@pages/shared-directory/search-widget';
import { EStatusGlobal, EStatusUser, GlobalFacade, RolesFacade, RolesModel, UserFacade, UserModel } from '@store';
import { formatDayjsDate, formatPhoneNumber } from '@utils';
import {
  Avatar,
  Button,
  Card,
  DatePicker,
  Drawer,
  Flex,
  Form,
  FormInstance,
  Modal,
  Select,
  Space,
  Spin,
  Switch,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import { SelectProps } from 'antd/lib';
import dayjs from 'dayjs';
import React, { useEffect, useRef } from 'react';
import { useNavigate } from 'react-router';
import { useSearchParams } from 'react-router-dom';
import { UserDrawer } from './user.drawer';

type TagRender = SelectProps['tagRender'];

const Page: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const formRef = useRef<FormInstance | undefined>(undefined);
  const userFacade = UserFacade();
  const rolesFacade = RolesFacade();
  const globalFacade = GlobalFacade();
  const [formFilter] = Form.useForm();
  const [modalApi, contextModalApi] = Modal.useModal();
  const {
    page,
    size,
    filter = '{}',
    sort = '',
    id = '',
  } = {
    ...Object.fromEntries(searchParams),
    page: Number(searchParams.get('page') || 1),
    size: Number(searchParams.get('size') || 20),
  };

  const parsedFilter = JSON.parse(filter);

  useEffect(() => {
    onChangeDataTable({});
    rolesFacade.get({ size: -1 });
    if (id) {
      userFacade.set({ isDetail: true });
    }

    if (!parsedFilter) return;

    // Format ngày nếu có
    const formattedFilter = {
      ...parsedFilter,
      // createdOnDate: parsedFilter.createdOnDate ? dayjs(parsedFilter.createdOnDate) : undefined,
      dateRange: parsedFilter.dateRange ? parsedFilter.dateRange.map((item: string) => dayjs(item)) : undefined,
    };

    // Set giá trị cho form
    formFilter.setFieldsValue(formattedFilter);
  }, []);

  useEffect(() => {
    switch (userFacade.status) {
      case EStatusState.postFulfilled:
      case EStatusState.putFulfilled:
      case EStatusState.deleteFulfilled:
      case EStatusUser.lockFulfilled:
      case EStatusUser.unlockFulfilled:
        onChangeDataTable({});
        userFacade.set({ isCreate: false, isEdit: false });
        break;
    }
  }, [userFacade.status]);

  useEffect(() => {
    switch (globalFacade.status) {
      case EStatusGlobal.changePasswordProfileFulfilled:
        onChangeDataTable({});
        userFacade.set({ isPassword: false });
        break;
    }
  }, [globalFacade.status]);

  const dataSource: UserModel[] =
    userFacade.pagination?.content.map((items, index) => ({
      lineNumber: (Number(searchParams.get('page') ?? 0) - 1) * Number(searchParams.get('size') ?? 0) + index + 1,
      id: items.id,
      ...items,
    })) ?? [];

  const handleEdit = (data: UserModel) => {
    userFacade.set({
      data: data,
      isEdit: true,
      isDetail: false,
      isPassword: false,
    });

    setSearchParams(
      (prev) => {
        if (!prev.has('id')) prev.append('id', data.id ?? '');
        else prev.set('id', data.id ?? '');
        return prev;
      },
      { replace: true },
    );
  };
  const handleViewDetail = (record: UserModel) => {
    if (record.id) {
      setSearchParams(
        {
          ...Object.fromEntries(searchParams),
          id: record.id,
        },
        { replace: true },
      );
      userFacade.set({ isDetail: true });
    }
  };

  const handleChangePassword = (record: UserModel) => {
    if (record.id) {
      setSearchParams(
        {
          ...Object.fromEntries(searchParams),
          id: record.id,
        },
        { replace: true },
      );
      userFacade.set({ isPassword: true });
    }
  };

  const handleLockedOut = (data: UserModel) => {
    data?.isLockedOut ? userFacade.unlock(data?.id ?? '') : userFacade.lock(data?.id ?? '');
  };

  const onChangeSearch = (value: string) => {
    if (value) {
      parsedFilter.fullTextSearch = value;
    } else {
      delete parsedFilter.fullTextSearch;
    }
    onChangeDataTable({
      query: {
        page: 1,
        size,
        filter: JSON.stringify({ ...parsedFilter }),
      },
    });
  };

  const onChangeDataTable = (props: { query?: QueryParams; setKeyState?: object }) => {
    if (!props.query) {
      props.query = {
        page,
        size,
        filter,
        sort,
        id,
      };
    }
    const fillQuery: QueryParams = { ...userFacade.query, ...props.query };
    for (const key in fillQuery) {
      if (!fillQuery[key as keyof QueryParams]) delete fillQuery[key as keyof QueryParams];
    }
    userFacade.get(fillQuery);
    navigate(
      { search: new URLSearchParams(fillQuery as unknown as Record<string, string>).toString() },
      { replace: true },
    );
    userFacade.set({ query: props.query, ...props.setKeyState });
  };

  const onFilter = (values: any) => {
    const currentFilter = JSON.parse(filter);
    Object.keys(values).forEach((key) => {
      if (values[key]) {
        if (['dateRange'].includes(key)) {
          currentFilter[key] = values[key].map((item: any) => dayjs(item).format('YYYY-MM-DD'));
        } else {
          currentFilter[key] = values[key];
        }
      } else {
        delete currentFilter[key];
      }
    });
    onChangeDataTable({
      query: {
        page: 1,
        size,
        filter: JSON.stringify({ ...currentFilter }),
      },
    });

    userFacade.set({ isFilterVisible: false });
  };

  const handleDelete = (record: UserModel) => {
    modalApi.confirm({
      title: 'Xác nhận xoá người dùng',
      content: `Người dùng ${record.name} sẽ bị xóa. Bạn có muốn tiếp tục?`,
      onOk: () => {
        record?.id && userFacade.delete(record.id);
      },
      onCancel: () => {},
      okText: 'Đồng ý',
      okButtonProps: { variant: 'outlined' },
      cancelText: 'Huỷ',
      cancelButtonProps: { danger: true },
    });
  };

  const columns: ColumnsType<UserModel> = [
    {
      title: 'STT',
      dataIndex: 'lineNumber',
      key: 'lineNumber',
      align: 'center',
      width: 50,
      fixed: 'left',
    },
    {
      title: 'Mã người dùng',
      dataIndex: 'code',
      key: 'code',
      fixed: 'left',
      width: 150,
      render: (value, record) => (
        <Tooltip title={'Xem chi tiết người dùng'}>
          <Typography.Link copyable onClick={() => handleViewDetail(record)}>
            {value}
          </Typography.Link>
        </Tooltip>
      ),
    },
    {
      title: 'Tên người dùng',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: 230,
      render: (value, record) => (
        <Space>
          <Avatar src={record.avatarUrl} alt={record?.name} icon={<UserOutlined />} />
          <Typography.Text>{value}</Typography.Text>
        </Space>
      ),
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 236,
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phoneNumber',
      key: 'phoneNumber',
      width: 120,
      render: (value) => formatPhoneNumber(value),
    },
    {
      title: 'Nhóm người dùng',
      dataIndex: 'role',
      key: 'role',
      width: 220,
      render: (_, record) => (
        <Flex wrap gap="4px 0">
          {record.listRole?.map((item: RolesModel) => (
            <Tag key={item.id} color={'#108ee9'}>
              {item?.name}
            </Tag>
          ))}
        </Flex>
      ),
    },
    {
      title: 'Trạng thái khóa',
      key: 'isLockedOut',
      width: 130,
      fixed: 'right',
      render: (item) => (
        <Switch
          className={`${item.isLockedOut ? '!bg-red-500' : '!bg-green-500'}`}
          checkedChildren="Đã khóa"
          unCheckedChildren="Chưa khóa"
          checked={item.isLockedOut}
          onChange={() => handleLockedOut(item)}
        />
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdOnDate',
      key: 'createdOnDate',
      width: 105,
      render: (value) => formatDayjsDate(value),
    },
    {
      title: 'Hành động',
      dataIndex: 'action',
      key: 'Action',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: (_, record: UserModel) => (
        <Space size={'small'}>
          <Tooltip title="Chỉnh sửa">
            <Button
              size="small"
              color="blue"
              variant="outlined"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              type={'link'}
            />
          </Tooltip>
          <Tooltip title="Đổi mật khẩu">
            <Button
              size="small"
              color="gold"
              variant="outlined"
              icon={<KeyOutlined />}
              onClick={() => handleChangePassword(record)}
              type={'link'}
            />
          </Tooltip>
          <Tooltip title="Xoá người dùng">
            <Button
              size="small"
              color="danger"
              variant="outlined"
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const keyLabelMap: Record<string, string> = {
    roleListCode: 'Nhóm người dùng',
    isLockedOut: 'Trạng thái khóa',
    dateRange: 'Ngày tạo',
    fullTextSearch: 'Tìm kiếm',
  };

  const isLockedOut = [
    { label: 'Đã khóa', value: 'true' },
    { label: 'Chưa khóa', value: 'false' },
  ];

  // Hàm ánh xạ giá trị từ value sang label
  const getValueLabel = (key: string, value: any) => {
    switch (key) {
      case 'roleListCode':
        return value
          .map((item: any) => rolesFacade.pagination?.content.find((role) => role.code === item)?.name || item)
          .join(', ');
      case 'isLockedOut':
        return isLockedOut.find((item) => item.value === value)?.label || value;
      case 'dateRange':
        return value?.map((item: any) => formatDayjsDate(item)).join(' - ');
      default:
        return value;
    }
  };

  const tagRender: TagRender = (props) => {
    const { label, value, closable, onClose } = props;
    const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
      event.preventDefault();
      event.stopPropagation();
    };
    return (
      <Tag
        className="px-3 rounded-full"
        color={'#E6F4FF'}
        onMouseDown={onPreventMouseDown}
        closable={closable}
        onClose={onClose}
        closeIcon={<CloseOutlined style={{ color: '#1890ff' }} />}
        style={{ marginInlineEnd: '0.25rem' }}
      >
        <span className="text-black text-sm">{label}</span>
      </Tag>
    );
  };

  return (
    <Spin spinning={userFacade.isLoading}>
      {contextModalApi}
      <SubHeader
        tool={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() =>
              userFacade.set({
                isCreate: true,
              })
            }
          >
            Tạo người dùng
          </Button>
        }
      />
      <div className="m-6 intro-x">
        <Card size="small" variant="borderless">
          <Flex className="!mb-2" gap={20} align="center">
            <InputSearch
              defaultValue={parsedFilter?.fullTextSearch}
              callback={onChangeSearch}
              placeholder={'Tìm kiếm theo mã nhân sự, tên nhân sự, chức vụ, phòng ban, email, số điện thoại'}
            />
            <Space className="float-right">
              <Select
                placeholder={'Chọn nhóm người dùng'}
                className="w-60"
                mode="multiple"
                tagRender={tagRender}
                value={parsedFilter.roleListCode}
                showSearch
                optionFilterProp="label"
                allowClear
                options={rolesFacade.pagination?.content.map((item) => ({
                  label: item.name,
                  value: item.code,
                }))}
                onChange={(value) => {
                  onChangeDataTable({
                    query: {
                      page: 1,
                      size,
                      filter: JSON.stringify({ ...parsedFilter, roleListCode: value }),
                    },
                  });
                }}
              />
              <Button
                icon={<ReloadOutlined />}
                loading={false}
                onClick={() => {
                  onChangeDataTable({ query: { page, size, sort, filter } });
                }}
              >
                Tải lại
              </Button>
              <Button
                icon={<FilterFilled />}
                iconPosition="end"
                onClick={() => userFacade.set({ isFilterVisible: true })}
              >
                Bộ lọc khác
              </Button>
            </Space>
          </Flex>
          {filter && (
            <Flex className="!mb-2" wrap gap="small">
              {Object.entries(parsedFilter).map(([key, value]) => {
                const keyName = keyLabelMap[key] || key;
                const valueName = getValueLabel(key, value as string);
                return (
                  <Tag
                    className="!rounded-full !py-0.5"
                    color="#E6F4FF"
                    key={key}
                    closable
                    closeIcon={
                      <CloseOutlined className="p-0.5 rounded hover:bg-slate-200" style={{ color: '#1890ff' }} />
                    }
                    onClose={() => {
                      const updatedFilter = { ...parsedFilter };
                      delete updatedFilter[key];

                      onChangeDataTable({
                        query: {
                          page: 1,
                          size,
                          filter: JSON.stringify(updatedFilter),
                        },
                      });

                      formFilter.setFieldsValue({ [key]: undefined });

                      switch (key) {
                        case 'fullTextSearch':
                          formRef.current?.resetFields(['search']);
                          break;
                      }
                    }}
                  >
                    <span className="text-black text-sm pl-0.5">
                      {keyName}: {valueName}
                    </span>
                  </Tag>
                );
              })}
            </Flex>
          )}
          <Table
            size="small"
            bordered
            columns={columns}
            dataSource={dataSource}
            pagination={{
              size: 'small',
              className: 'pr-4',
              showSizeChanger: true,
              current: userFacade?.pagination?.page,
              pageSize: userFacade?.pagination?.size,
              total: userFacade?.pagination?.totalElements,
              pageSizeOptions: [20, 40, 60, 80],
              showTotal: (total, range) => `Từ ${range[0]} đến ${range[1]} trên tổng ${total}`,
              onChange: (page, size) => {
                onChangeDataTable({
                  query: {
                    page,
                    size,
                    filter: JSON.stringify({ ...parsedFilter }),
                  },
                });
              },
            }}
            rowKey="id"
            scroll={{ x: 'max-content', y: 'calc(100vh - 350px)' }}
          />
        </Card>
      </div>
      {/* <Flex className="px-5 mt-5" vertical gap={1.5}>
        <Table
          title={() => (
            <>
              <Flex gap={15} align="center">
                <div className="flex-1">
                  <InputSearch
                    defaultValue={parsedFilter?.fullTextSearch}
                    callback={onChangeSearch}
                    placeholder={'Tìm kiếm theo mã nhân sự, tên nhân sự, chức vụ, phòng ban, email, số điện thoại'}
                  />
                </div>
                <Select
                  placeholder={'Chọn nhóm người dùng'}
                  className="w-60"
                  mode="multiple"
                  tagRender={tagRender}
                  value={parsedFilter.roleListCode}
                  showSearch
                  optionFilterProp="label"
                  allowClear
                  options={rolesFacade.pagination?.content.map((item) => ({
                    label: item.name,
                    value: item.code,
                  }))}
                  onChange={(value) => {
                    onChangeDataTable({
                      query: {
                        page: 1,
                        size,
                        filter: JSON.stringify({ ...parsedFilter, roleListCode: value }),
                      },
                    });
                  }}
                />
                <Button
                  icon={<ReloadOutlined />}
                  loading={false}
                  onClick={() => {
                    onChangeDataTable({ query: { page, size, sort, filter } });
                  }}
                >
                  Tải lại
                </Button>
                <Button
                  icon={<FilterFilled />}
                  iconPosition="end"
                  onClick={() => userFacade.set({ isFilterVisible: true })}
                >
                  Bộ lọc khác
                </Button>
              </Flex>
              {filter && (
                <Flex className="mt-3" wrap gap="small">
                  {Object.entries(parsedFilter).map(([key, value]) => {
                    const keyName = keyLabelMap[key] || key;
                    const valueName = getValueLabel(key, value as string);
                    return (
                      <Tag
                        className="rounded-full py-0.5"
                        color="#E6F4FF"
                        key={key}
                        closable
                        closeIcon={
                          <CloseOutlined className="p-0.5 rounded hover:bg-slate-200" style={{ color: '#1890ff' }} />
                        }
                        onClose={() => {
                          const updatedFilter = { ...parsedFilter };
                          delete updatedFilter[key];

                          onChangeDataTable({
                            query: {
                              page: 1,
                              size,
                              filter: JSON.stringify(updatedFilter),
                            },
                          });

                          formFilter.setFieldsValue({ [key]: undefined });

                          switch (key) {
                            case 'fullTextSearch':
                              formRef.current?.resetFields(['search']);
                              break;
                          }
                        }}
                      >
                        <span className="text-black text-[14px] pl-0.5 h-">
                          {keyName}: {valueName}
                        </span>
                      </Tag>
                    );
                  })}
                </Flex>
              )}
            </>
          )}
          columns={columns}
          dataSource={dataSource}
          pagination={{
            size: 'small',
            className: 'pr-4',
            showSizeChanger: true,
            current: userFacade?.pagination?.page,
            pageSize: userFacade?.pagination?.size,
            total: userFacade?.pagination?.totalElements,
            pageSizeOptions: [20, 40, 60, 80],
            showTotal: (total, range) => `Từ ${range[0]} đến ${range[1]} trên tổng ${total}`,
            onChange: (page, size) => {
              let query = userFacade.query;
              query = { ...query, page: page, size: size };
              onChangeDataTable({ query: query });
            },
          }}
          rowKey="id"
          scroll={{ x: 'max-content', y: 'calc(100vh - 350px)' }}
        />
      </Flex> */}

      <Drawer
        title={'Bộ lọc'}
        maskClosable={false}
        forceRender
        open={userFacade.isFilterVisible}
        onClose={() => userFacade.set({ isFilterVisible: false })}
        closeIcon={false}
        extra={
          <Button type={'text'} icon={<CloseOutlined />} onClick={() => userFacade.set({ isFilterVisible: false })} />
        }
        footer={
          <Space className={'flex justify-end'}>
            <Button danger onClick={() => formFilter.resetFields()}>
              Xóa bộ lọc
            </Button>
            <Button type={'primary'} onClick={formFilter.submit}>
              Lọc
            </Button>
          </Space>
        }
      >
        <Form form={formFilter} layout={'vertical'} onFinish={onFilter}>
          <Form.Item name={'roleListCode'} label={'Nhóm người dùng'}>
            <Select
              placeholder={'Chọn nhóm người dùng'}
              showSearch
              mode="multiple"
              value={parsedFilter.roleListCode}
              tagRender={tagRender}
              allowClear
              optionFilterProp="label"
              options={rolesFacade.pagination?.content.map((item) => ({
                label: item.name,
                value: item.code,
              }))}
            />
          </Form.Item>
          <Form.Item name={'isLockedOut'} label={'Trạng thái khóa'}>
            <Select
              placeholder={'Chọn trạng thái'}
              showSearch
              allowClear
              optionFilterProp="label"
              options={[
                {
                  label: (
                    <Tag color={'green'} key={'Chưa khóa'}>
                      Chưa khóa
                    </Tag>
                  ),
                  value: 'false',
                },
                {
                  label: (
                    <Tag color={'red'} key={'Đã khóa'}>
                      Đã khóa
                    </Tag>
                  ),
                  value: 'true',
                },
              ]}
            />
          </Form.Item>
          <Form.Item name={'dateRange'} label={'Ngày tạo'}>
            <DatePicker.RangePicker
              className="w-full"
              format={'DD/MM/YYYY'}
              placement="bottomRight"
              allowClear
              presets={[
                {
                  label: 'Hôm nay',
                  value: [dayjs().startOf('day'), dayjs().endOf('day')],
                },
                {
                  label: 'Tuần này',
                  value: [dayjs().startOf('week'), dayjs().endOf('week')],
                },
                {
                  label: 'Tháng này',
                  value: [dayjs().startOf('month'), dayjs().endOf('month')],
                },
                {
                  label: 'Tuần trước',
                  value: [dayjs().subtract(1, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week')],
                },
                {
                  label: 'Tháng trước',
                  value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')],
                },
              ]}
            />
          </Form.Item>
        </Form>
      </Drawer>

      <UserDrawer />
    </Spin>
  );
};

export default Page;
