{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --force", "build": "tsc && vite build", "serve": "npx serve build -l 4000", "build:dev": "tsc && vite build", "build:stag": "tsc && vite build --mode staging", "build:prod": "tsc && vite build --mode prod", "preview": "vite preview", "format": "prettier --write \"{src,public}/**/*.{tsx,jsx}\"", "lint": "eslint \"{src,public}/**/*.{tsx,jsx}\" --fix", "update-all": "npx -p npm-check-updates ncu -u && npm install"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@nivo/bar": "^0.88.0", "@nivo/bump": "^0.88.0", "@nivo/core": "^0.88.0", "@nivo/line": "^0.88.0", "@reduxjs/toolkit": "^2.2.1", "@tailwindcss/vite": "^4.1.4", "antd": "^5.25.1", "antd-input-otp": "^2.0.5", "antd-mobile": "^5.39.0", "echarts": "^5.5.0", "echarts-for-react": "^3.0.2", "i18next": "^24.2.3", "i18next-xhr-backend": "^3.2.2", "inputmask": "^5.0.8", "react": "^18.2.0", "react-countup": "^6.5.3", "react-dom": "^18.2.0", "react-i18next": "^14.0.5", "react-redux": "^9.1.0", "react-router": "^6.22.1", "react-router-dom": "^6.22.1", "react-to-print": "^3.0.2", "slug": "^8.2.3", "socket.io-client": "^4.7.4", "swiper": "^11.0.6", "tailwindcss": "^4.1.4", "web-vitals": "^3.5.2"}, "devDependencies": {"@types/node": "^20.11.20", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "@types/slug": "^5.0.7", "@typescript-eslint/eslint-plugin": "^7.0.2", "@vitejs/plugin-react-swc": "^3.6.0", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-refresh": "^0.4.5", "less": "^4.2.0", "less-loader": "^12.2.0", "prettier": "^3.2.5", "rc-tween-one": "^3.0.6", "suneditor": "^2.45.1", "suneditor-react": "^3.6.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "5.3.3", "vite": "^5.1.3", "vite-plugin-pwa": "^1.0.0", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}