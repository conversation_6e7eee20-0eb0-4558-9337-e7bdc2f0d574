import { EStatusMachine, MachinesFacade } from '@store';
import { formatAmount, formatDayjsDate, uuidv4 } from '@utils';
import { Button as AntButton, Image, Space as AntSpace } from 'antd';
import { Button, Card, DotLoading, Grid, PullToRefresh, Space, Tabs, Tag } from 'antd-mobile';
import { CameraOutline } from 'antd-mobile-icons';
import { useEffect } from 'react';
import { useOutletContext, useParams, useNavigate } from 'react-router';
import { useIsMobile } from 'src/utils/hooks/useIsMobile';
import { T_MasterCtx } from '../../layouts/detail';
import {
  AreaChartOutlined,
  UploadOutlined
} from '@ant-design/icons';
import MachineConfiguration from './components/MachineConfiguration';
import MachineCommand from './components/MachineCommand';
import MachineOverview from './components/MachineOverview';

export default function Page() {
  const machinesFacade = MachinesFacade();
  const { name } = useParams();
  const navigate = useNavigate();

  const { machineName } = useOutletContext<T_MasterCtx>();
  const [, setMachineName] = machineName;

  const isMobile = useIsMobile();

  useEffect(() => {
    if (name) {
      setMachineName(name);
    }
  }, [name, setMachineName]);

  useEffect(() => {
    if (name) {
      machinesFacade.getByName({ name });
    }
  }, []);

  useEffect(() => {
    if (name && !isMobile) {
      machinesFacade.getImagesTest({ name, params: {} });
    }
  }, [name, isMobile]);

  useEffect(() => {
    switch (machinesFacade.status) {
      case EStatusMachine.postDeviceCommandFulfilled:
        name && machinesFacade.getImagesTest({ name, params: {} });
        break;
    }
  }, [machinesFacade.status]);

  const onChangeTabs = (key: string) => {
    console.log('Tab changed to:', key, 'with name:', name);
    if (name) {
      switch (key) {
        case 'images_test':
          machinesFacade.getImagesTest({ name, params: {} });
          break;
      }
    }
  };

  const renderImageTest = () => {
    const imageGrid = (
      <>
        {machinesFacade.imagesTest?.content && machinesFacade.imagesTest?.content.length > 0 ? (
          <Grid columns={isMobile ? 2 : 3} gap={8}>
            <Image.PreviewGroup>
              {machinesFacade.imagesTest?.content?.map((item) => (
                <Grid.Item key={item.id}>
                  <Image src={item.url} alt={item.filename} />
                  <i className="text-center block">{formatDayjsDate(item.createdOnDate, 'HH:mm:ss DD/MM')}</i>
                </Grid.Item>
              ))}
            </Image.PreviewGroup>
          </Grid>
        ) : (
          <div style={{ textAlign: 'center', padding: '2rem', color: '#999' }}>Chưa có ảnh test</div>
        )}
      </>
    );
    return (
      <div className="mt-4">
        <h2 className="text-lg font-semibold mb-2">Ảnh chụp test</h2>
        <div className="flex items-center justify-center mb-3">
          <Button
            loading={machinesFacade.isLoading}
            color="warning"
            fill="outline"
            onClick={() => {
              name &&
                machinesFacade.postDeviceCommand({
                  deviceId: name,
                  values: {
                    name: 'capture_test',
                    value: '1000',
                    type: 'PULL_CMD',
                    service: 'MAIN',
                  },
                });
            }}
          >
            <Space align="center">
              <CameraOutline />
              <span>Capture</span>
            </Space>
          </Button>
        </div>
        {isMobile ? (
          <PullToRefresh
            canReleaseText="Thả để làm mới"
            completeText="Đã làm mới"
            pullingText="Kéo xuống để làm mới"
            refreshingText={
              <>
                <DotLoading /> Đang làm mới...
              </>
            }
            onRefresh={async () => {
              name && machinesFacade.getImagesTest({ name, params: {} });
            }}
          >
            {imageGrid}
          </PullToRefresh>
        ) : (
          imageGrid
        )}
      </div>
    );
  };

  return (
    <>
      <div className="mx-auto bg-white">
        {isMobile ? (
          <Tabs style={{ '--title-font-size': '15px' }} className="bg-white" onChange={onChangeTabs}>
            <Tabs.Tab title="Tổng quan" key="overview">
              <MachineOverview name={name} isMobile={isMobile} />
            </Tabs.Tab>
            <Tabs.Tab title="Cấu hình" key="configuration">
              <MachineConfiguration name={name} />
            </Tabs.Tab>
            <Tabs.Tab title="Điều khiển" key="control">
              <MachineCommand name={name} />
            </Tabs.Tab>
            <Tabs.Tab title="Ảnh test" key="images_test">
              {renderImageTest()}
            </Tabs.Tab>
          </Tabs>
        ) : (
          <div className="p-4">
            <Grid columns={12} gap={16}>
              <Grid.Item span={5}>
                <MachineOverview name={name} isMobile={isMobile} />
                {renderImageTest()}
              </Grid.Item>
              <Grid.Item span={4}>
                <h2 className="text-lg font-semibold mb-2">Điều khiển</h2>
                <MachineCommand name={name} />
              </Grid.Item>
              <Grid.Item span={3}>
                <AntSpace className="mb-4">
                  <AntButton
                    style={{ backgroundColor: '#f0c674', color: 'black', border: 'none' }}
                    onClick={() => navigate(`/machine/${name}/chart`)}
                  >
                    <AreaChartOutlined /> Biểu đồ thông số
                  </AntButton>
                  <AntButton
                    style={{ backgroundColor: '#f0c674', color: 'black', border: 'none' }}
                    onClick={() => navigate(`/machine/${name}/upload`)}
                  >
                    <UploadOutlined /> Tải tệp tin lên
                  </AntButton>
                </AntSpace>
                <h2 className="text-lg font-semibold mb-2">Cấu hình</h2>
                <MachineConfiguration name={name} />
              </Grid.Item>
            </Grid>
          </div>
        )}
      </div>
    </>
  );
}
