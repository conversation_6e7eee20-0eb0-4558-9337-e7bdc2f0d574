import React from 'react';

export const Message = {
  success: ({ content = '' }: Type) =>
    import('antd').then(({ message }) =>
      message.success({
        content,
      }),
    ),
  error: ({ content = '' }: Type) =>
    import('antd').then(({ message }) =>
      message.error({
        content,
      }),
    ),
  warning: ({ content = '' }: Type) =>
    import('antd').then(({ message }) =>
      message.warning({
        content,
      }),
    ),
  info: ({ content = '' }: Type) =>
    import('antd').then(({ message }) =>
      message.info({
        content,
      }),
    ),
  open: ({ content }: Type) =>
    import('antd').then(({ message }) =>
      message.open({
        content,
      }),
    ),
};

type Type = {
  content: React.ReactNode;
};
