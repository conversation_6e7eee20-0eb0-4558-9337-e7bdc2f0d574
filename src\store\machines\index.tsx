import { Draft, createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { Message } from '@core/message';
import { CommonEntity, EStatusState, Pagination, QueryParams, Responses } from '@models';
import { Action, Slice, State, useAppDispatch, useTypedSelector } from '@store';
import { API } from '@utils';
import { message } from 'antd';

export interface Attachment {
  id: string;
  docType: string;
  docTypeName: string;
  entityType?: string;
  file?: string;
  prefix?: string;
  description: string;
  createdByUserId: string;
  createdOnDate: string;
}

const name = 'Machines';
const action = {
  ...new Action<MachineModel, EStatusMachine>(name),
  getMachines: createAsyncThunk(name + '/getMachines', async (params: QueryParams) => {
    const res = await API.get<Pagination<MachineModel>>('https://api-monitor.autotimelapse.com/api/devices', params);
    return res;
  }),
  getMachineConfig: createAsyncThunk(name + '/getMachineConfig', async (deviceName: string) => {
    const res = await API.get<MachineConfigModel[]>(
      `https://api-monitor.autotimelapse.com/api/devices/${deviceName}/config`,
    );
    return res;
  }),
  getDeviceLogs: createAsyncThunk(name + '/getDeviceLogs', async (params: { name: string; size?: number; keyword?: string; timeRanges?: string[] }) => {
    const { name, ...queryParams } = params;
    const res = await API.get(`https://api-monitor.autotimelapse.com/api/devices/${name}/log`, queryParams);
    return res;
  }),
  getDeviceFileLogs: createAsyncThunk(name + '/getDeviceFileLogs', async (params: { name: string; size?: number; keyword?: string; timeRanges?: string[], logType?: 'Main' | 'Backup' }) => {
    const { name, ...queryParams } = params;
    const res = await API.get(`https://api-monitor.autotimelapse.com/api/devices/${name}/log/sync`, queryParams);
    return res;
  }),
  getByName: createAsyncThunk(name + '/getByName', async (params: { name: string }) => {
    const res = await API.get(`https://api-monitor.autotimelapse.com/api/devices/${params.name}`);
    return res;
  }),
  getMetadata: createAsyncThunk(
    name + '/getMetadata',
    async ({ name, params }: { name: string; params: QueryParams }) => {
      const res = await API.get(`https://api-monitor.autotimelapse.com/api/devices/${name}/metadata`, params);
      return res;
    },
  ),
  getErrorList: createAsyncThunk(
    name + '/getErrorList',
    async ({ name, params }: { name: string; params: QueryParams }) => {
      const res = await API.get(`https://api-monitor.autotimelapse.com/api/devices/${name}/errors`, params);
      return res;
    },
  ),
  getImagesTest: createAsyncThunk(
    name + '/getImagesTest',
    async ({ name, params }: { name: string; params: QueryParams }) => {
      const res = await API.get(`https://api-monitor.autotimelapse.com/api/devices/${name}/images`, params);
      return res;
    },
  ),
  getByIdToAttachmentsTemplate: createAsyncThunk(
    name + '/getByIdToAttachmentsTemplate',
    async ({ name }: { name: string }) =>
      API.get<Attachment[]>(`https://api-monitor.autotimelapse.com/api/devices/${name}/attachments/template`),
  ),
  getAttachments: createAsyncThunk(
    name + '/getAttachments',
    async ({ name, docType }: { name: string; docType: string }) =>
      API.get<Attachment[]>(`https://api-monitor.autotimelapse.com/api/devices/${name}/attachments`, {
        docType,
      }),
  ),
  createAttachmentsTemplate: createAsyncThunk(
    name + '/createAttachmentsTemplate',
    async ({ name, data }: { name: string; data: any }) =>
      API.post(`https://api-monitor.autotimelapse.com/api/devices/${name}/attachments`, data),
  ),
  deleteImage: createAsyncThunk(name + '/deleteImage', async ({ name, id }: { name: string; id: string }) =>
    API.delete(`https://api-monitor.autotimelapse.com/api/devices/${name}/attachments/${id}`),
  ),
  getDeviceMainConfig: createAsyncThunk(
    name + '/getDeviceMainConfig',
    async (params: { deviceId: string; page?: number; size?: number; filter?: string; sort?: string }) => {
      const res = await API.get(`https://api-monitor.autotimelapse.com/api/main-config`, params);
      return res;
    },
  ),
  postDeviceCommand: createAsyncThunk(
    name + 'postDeviceCommand',
    async ({
      deviceId,
      values,
    }: {
      deviceId: string;
      values: { service: string; name: string; value: string; type: string };
    }) => {
      const { message } = await API.post(
        `https://api-monitor.autotimelapse.com/api/devices/${deviceId}/commands`,
        values,
      );
      if (message) Message.success({ content: message });
      return message;
    },
  ),
  getDeviceCommand: createAsyncThunk(name + '/getDeviceCommand', async (deviceId: string) => {
    const res = await API.get<Pagination<DeviceCommandModel>>(
      `https://api-monitor.autotimelapse.com/api/devices/${deviceId}/commands`,
    );
    return res;
  }),
  getButtonCommand: createAsyncThunk(name + '/getButtonCommand', async () => {
    const res = await API.get<ButtonCommandResponse>(`https://api-monitor.autotimelapse.com/api/command/button`);
    return res;
  }),
  changeConfig: createAsyncThunk(
    name + '/changeConfig',
    async ({ deviceName, configs }: { deviceName: string; configs: MachineConfigModel[] }) => {
      const { message } = await API.post(
        `https://api-monitor.autotimelapse.com/api/devices/${deviceName}/config`,
        configs,
      );
      if (message) Message.success({ content: message });
      return message;
    },
  ),
  deleteDeviceError: createAsyncThunk(
    name + 'deleteDeviceError',
    async ({ name, errorCode }: { name: string; errorCode: string }) => {
      const { message } = await API.delete(
        `https://api-monitor.autotimelapse.com/api/devices/${name}/errors/${errorCode}`,
      );
      if (message) Message.success({ content: message });
      return message;
    },
  ),
  deleteDeviceCommand: createAsyncThunk(
    name + '/deleteDeviceCommand',
    async ({ deviceName, commandId }: { deviceName: string; commandId: string }) => {
      const { message } = await API.delete(
        `https://api-monitor.autotimelapse.com/api/devices/${deviceName}/commands/${commandId}`,
      );
      if (message) Message.success({ content: message });
      return message;
    },
  ),
  getBatteryChart: createAsyncThunk(
    name + '/getBatteryChart',
    async ({ deviceId, count = 120 }: { deviceId: string; count?: number }) => {
      const res = await API.get(
        `https://api-monitor.autotimelapse.com/api/devices/${deviceId}/time-series/battery`,
        { count }
      );
      return res;
    },
  ),
  getTemperatureChart: createAsyncThunk(
    name + '/getTemperatureChart',
    async ({ deviceId, count = 20 }: { deviceId: string; count?: number }) => {
      const res = await API.get(
        `https://api-monitor.autotimelapse.com/api/devices/${deviceId}/time-series/temperature`,
        { count }
      );
      return res;
    },
  ),
  getHumidityChart: createAsyncThunk(
    name + '/getHumidityChart',
    async ({ deviceId, count = 20 }: { deviceId: string; count?: number }) => {
      const res = await API.get(
        `https://api-monitor.autotimelapse.com/api/devices/${deviceId}/time-series/humidity`,
        { count }
      );
      return res;
    },
  ),
  getUploadSpeedChart: createAsyncThunk(
    name + '/getUploadSpeedChart',
    async ({ deviceId, count = 20 }: { deviceId: string; count?: number }) => {
      const res = await API.get(
        `https://api-monitor.autotimelapse.com/api/devices/${deviceId}/time-series/upload_speed`,
        { count }
      );
      return res;
    },
  ),
};

export const machinesSlice = createSlice({
  ...new Slice<MachineModel, EStatusMachine>(
    action,
    {
      keepUnusedDataFor: 9999,
      listUpload: {},
      showList: {},
      description: {},
      infoUpload: [],
    },
    (builder) => {
      builder
        .addCase(action.getMachines.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getMachinesPending;
        })
        .addCase(action.getMachines.fulfilled, (state, action) => {
          if (action.payload) {
            state.pagination = action.payload.data as Draft<Pagination<MachineModel>>;
            state.status = EStatusMachine.getMachinesFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getMachines.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getMachinesRejected;
        })
        .addCase(action.getMachineConfig.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getMachineConfigPending;
        })
        .addCase(action.getMachineConfig.fulfilled, (state, action) => {
          if (action.payload) {
            state.machineConfig = action.payload.data as Draft<MachineConfigModel[]>;
            state.status = EStatusMachine.getMachineConfigFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getMachineConfig.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getMachineConfigRejected;
        })
        .addCase(action.getDeviceLogs.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getDeviceLogsPending;
        })
        .addCase(action.getDeviceLogs.fulfilled, (state, action) => {
          if (action.payload) {
            state.deviceLogs = action.payload.logs;
            state.status = EStatusMachine.getDeviceLogsFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getDeviceLogs.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getDeviceLogsRejected;
        })
        .addCase(action.getDeviceFileLogs.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getDeviceFileLogsPending;
        })
        .addCase(action.getDeviceFileLogs.fulfilled, (state, action) => {
          if (action.payload) {
            state.deviceFileLogs = action.payload.logs;
            state.status = EStatusMachine.getDeviceFileLogsFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getDeviceFileLogs.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getDeviceFileLogsRejected;
        })
        .addCase(action.getByName.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getByNamePending;
        })
        .addCase(action.getByName.fulfilled, (state, action) => {
          if (action.payload) {
            state.data = action.payload as Draft<MachineModel>;
            state.status = EStatusMachine.getByNameFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getByName.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getByNameRejected;
        })
        .addCase(action.getMetadata.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getMetadataPending;
        })
        .addCase(action.getMetadata.fulfilled, (state, action) => {
          if (action.payload) {
            state.metadata = action.payload.data;
            state.status = EStatusMachine.getMetadataFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getMetadata.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getMetadataRejected;
        })
        .addCase(action.getErrorList.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getErrorListPending;
        })
        .addCase(action.getErrorList.fulfilled, (state, action) => {
          if (action.payload) {
            state.errorList = action.payload.data;
            state.status = EStatusMachine.getErrorListFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getErrorList.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getErrorListRejected;
        })
        .addCase(action.getImagesTest.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getImagesTestPending;
        })
        .addCase(action.getImagesTest.fulfilled, (state, action) => {
          if (action.payload) {
            state.imagesTest = action.payload.data;
            state.status = EStatusMachine.getImagesTestFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getImagesTest.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getImagesTestRejected;
        })
        .addCase(action.getDeviceMainConfig.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getDeviceMainConfigPending;
        })
        .addCase(action.getDeviceMainConfig.fulfilled, (state, action) => {
          if (action.payload) {
            state.deviceMainConfig = action.payload.data;
            state.status = EStatusMachine.getDeviceMainConfigFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getDeviceMainConfig.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getDeviceMainConfigRejected;
        })
        .addCase(action.postDeviceCommand.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.postDeviceCommandPending;
        })
        .addCase(action.postDeviceCommand.fulfilled, (state, action) => {
          state.isLoading = false;
          state.status = EStatusMachine.postDeviceCommandFulfilled;
        })
        .addCase(action.postDeviceCommand.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.postDeviceCommandRejected;
        })
        .addCase(action.changeConfig.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.changeConfigPending;
        })
        .addCase(action.changeConfig.fulfilled, (state, action) => {
          state.isLoading = false;
          state.status = EStatusMachine.changeConfigFulfilled;
        })
        .addCase(action.changeConfig.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.changeConfigRejected;
        })
        .addCase(action.deleteDeviceError.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.deleteDeviceErrorPending;
        })
        .addCase(action.deleteDeviceError.fulfilled, (state, action) => {
          state.isLoading = false;
          state.status = EStatusMachine.deleteDeviceErrorFulfilled;
        })
        .addCase(action.deleteDeviceError.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.deleteDeviceErrorRejected;
        })
        .addCase(action.getByIdToAttachmentsTemplate.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getByIdToAttachmentsTemplatePending;
        })
        .addCase(action.getByIdToAttachmentsTemplate.fulfilled, (state, action) => {
          if (action.payload) {
            state.infoUpload = action.payload;
            state.status = EStatusMachine.getByIdToAttachmentsTemplateFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getByIdToAttachmentsTemplate.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getByIdToAttachmentsTemplateRejected;
        })
        .addCase(action.getAttachments.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getAttachmentsPending;
        })
        .addCase(action.getAttachments.fulfilled, (state, action) => {
          state.listUpload = {
            ...state.listUpload,
            [action.meta.arg.docType]: action.payload.data,
          };
          state.showList = { ...state.showList, [action.meta.arg.docType]: false };
          state.description = { ...state.description, [action.meta.arg.docType]: '' };
          state.isLoading = false;
          state.status = EStatusMachine.getAttachmentsFulfilled;
        })
        .addCase(action.getAttachments.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getAttachmentsRejected;
        })
        .addCase(action.createAttachmentsTemplate.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.createAttachmentsTemplatePending;
        })
        .addCase(action.createAttachmentsTemplate.fulfilled, (state, action) => {
          state.isLoading = false;
          state.status = EStatusMachine.createAttachmentsTemplateFulfilled;
          message.success(action.payload.message);
        })
        .addCase(action.createAttachmentsTemplate.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.createAttachmentsTemplateRejected;
        })
        .addCase(action.deleteImage.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.deleteImagePending;
        })
        .addCase(action.deleteImage.fulfilled, (state, action) => {
          state.isLoading = false;
          state.status = EStatusMachine.deleteImageFulfilled;
          message.success(action.payload.message);
        })
        .addCase(action.deleteImage.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.deleteImageRejected;
        })
        .addCase(action.getDeviceCommand.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getDeviceCommandPending;
        })
        .addCase(action.getDeviceCommand.fulfilled, (state, action) => {
          if (action.payload) {
            state.deviceCommands = action.payload.data?.content as Draft<DeviceCommandModel[]>;
            state.status = EStatusMachine.getDeviceCommandFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getDeviceCommand.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getDeviceCommandRejected;
        })
        .addCase(action.getButtonCommand.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusMachine.getButtonCommandPending;
        })
        .addCase(action.getButtonCommand.fulfilled, (state, action) => {
          if (action.payload) {
            state.buttonCommands = action.payload.data as Draft<ButtonCommandResponse>;
            state.status = EStatusMachine.getButtonCommandFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getButtonCommand.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusMachine.getButtonCommandRejected;
        })
              .addCase(action.deleteDeviceCommand.pending, (state) => {
        state.isLoading = true;
        state.status = EStatusMachine.deleteDeviceCommandPending;
      })
      .addCase(action.deleteDeviceCommand.fulfilled, (state, action) => {
        state.isLoading = false;
        state.status = EStatusMachine.deleteDeviceCommandFulfilled;
      })
      .addCase(action.deleteDeviceCommand.rejected, (state) => {
        state.isLoading = false;
        state.status = EStatusMachine.deleteDeviceCommandRejected;
      })
      .addCase(action.getBatteryChart.pending, (state) => {
        state.isLoading = true;
        state.status = EStatusMachine.getBatteryChartPending;
      })
      .addCase(action.getBatteryChart.fulfilled, (state, action) => {
        if (action.payload) {
          state.batteryChart = action.payload.data as Draft<any>;
          state.status = EStatusMachine.getBatteryChartFulfilled;
        } else state.status = EStatusState.idle;
        state.isLoading = false;
      })
      .addCase(action.getBatteryChart.rejected, (state) => {
        state.isLoading = false;
        state.status = EStatusMachine.getBatteryChartRejected;
      })
      .addCase(action.getTemperatureChart.pending, (state) => {
        state.isLoading = true;
        state.status = EStatusMachine.getTemperatureChartPending;
      })
      .addCase(action.getTemperatureChart.fulfilled, (state, action) => {
        if (action.payload) {
          state.temperatureChart = action.payload.data as Draft<any>;
          state.status = EStatusMachine.getTemperatureChartFulfilled;
        } else state.status = EStatusState.idle;
        state.isLoading = false;
      })
      .addCase(action.getTemperatureChart.rejected, (state) => {
        state.isLoading = false;
        state.status = EStatusMachine.getTemperatureChartRejected;
      })
      .addCase(action.getHumidityChart.pending, (state) => {
        state.isLoading = true;
        state.status = EStatusMachine.getHumidityChartPending;
      })
      .addCase(action.getHumidityChart.fulfilled, (state, action) => {
        if (action.payload) {
          state.humidityChart = action.payload.data as Draft<any>;
          state.status = EStatusMachine.getHumidityChartFulfilled;
        } else state.status = EStatusState.idle;
        state.isLoading = false;
      })
      .addCase(action.getHumidityChart.rejected, (state) => {
        state.isLoading = false;
        state.status = EStatusMachine.getHumidityChartRejected;
      })
      .addCase(action.getUploadSpeedChart.pending, (state) => {
        state.isLoading = true;
        state.status = EStatusMachine.getUploadSpeedChartPending;
      })
      .addCase(action.getUploadSpeedChart.fulfilled, (state, action) => {
        if (action.payload) {
          state.uploadSpeedChart = action.payload.data as Draft<any>;
          state.status = EStatusMachine.getUploadSpeedChartFulfilled;
        } else state.status = EStatusState.idle;
        state.isLoading = false;
      })
      .addCase(action.getUploadSpeedChart.rejected, (state) => {
        state.isLoading = false;
        state.status = EStatusMachine.getUploadSpeedChartRejected;
        });
      })
    }
  )


export const MachinesFacade = () => {
  const dispatch = useAppDispatch();
  return {
    ...useTypedSelector((state) => state[action.name] as StateMachines<MachineModel>),
    set: (values: StateMachines<MachineModel>) => dispatch(action.set(values)),
    get: (params: QueryParams) => dispatch(action.get(params)),
    getMachines: (params: { time?: number; page?: number; size?: number; filter?: string }) =>
      dispatch(action.getMachines(params)),
    getMachineConfig: (deviceName: string) => dispatch(action.getMachineConfig(deviceName)),
    getById: ({ id, keyState = 'isVisible' }: { id: string; keyState?: keyof StateMachines<MachineModel> }) =>
      dispatch(action.getById({ id, keyState })),
    post: (values: MachineModel) => dispatch(action.post({ values })),
    put: (values: MachineModel) => dispatch(action.put({ values })),
    putDisable: (values: { id: string; disable: boolean }) => dispatch(action.putDisable(values)),
    delete: (values: { id: string }) => dispatch(action.delete(values)),
    getDeviceLogs: (params: { name: string; size?: number; keyword?: string; timeRanges?: string[] }) => dispatch(action.getDeviceLogs(params)),
    getDeviceFileLogs: (params: { name: string; size?: number; keyword?: string; timeRanges?: string[], logType?: 'Main' | 'Backup' }) => dispatch(action.getDeviceFileLogs(params)),
    getByName: (params: { name: string }) => dispatch(action.getByName(params)),
    getMetadata: ({ name, params }: { name: string; params: QueryParams }) =>
      dispatch(action.getMetadata({ name, params })),
    getErrorList: ({ name, params }: { name: string; params: QueryParams }) =>
      dispatch(action.getErrorList({ name, params })),
    getImagesTest: ({ name, params }: { name: string; params: QueryParams }) =>
      dispatch(action.getImagesTest({ name, params })),
    getDeviceMainConfig: (params: { deviceId: string; page?: number; size?: number; filter?: string; sort?: string }) =>
      dispatch(action.getDeviceMainConfig(params)),
    postDeviceCommand: (params: {
      deviceId: string;
      values: { service: string; name: string; value: string; type: string };
    }) => dispatch(action.postDeviceCommand(params)),
    changeConfig: (params: { deviceName: string; configs: MachineConfigModel[] }) =>
      dispatch(action.changeConfig(params)),
    getByIdToAttachmentsTemplate: (name: string) => dispatch(action.getByIdToAttachmentsTemplate({ name })),
    getAttachments: (params: { name: string; docType: string }) => dispatch(action.getAttachments(params)),
    createAttachmentsTemplate: (name: string, data: any) => dispatch(action.createAttachmentsTemplate({ name, data })),
    deleteImage: (name: string, id: string) => dispatch(action.deleteImage({ name, id })),
    deleteDeviceError: (params: { name: string; errorCode: string }) => dispatch(action.deleteDeviceError(params)),
    getDeviceCommand: (deviceId: string) => dispatch(action.getDeviceCommand(deviceId)),
    getButtonCommand: () => dispatch(action.getButtonCommand()),
    deleteDeviceCommand: (params: { deviceName: string; commandId: string }) => dispatch(action.deleteDeviceCommand(params)),
    getBatteryChart: (params: { deviceId: string; count?: number }) => dispatch(action.getBatteryChart(params)),
    getTemperatureChart: (params: { deviceId: string; count?: number }) => dispatch(action.getTemperatureChart(params)),
    getHumidityChart: (params: { deviceId: string; count?: number }) => dispatch(action.getHumidityChart(params)),
    getUploadSpeedChart: (params: { deviceId: string; count?: number }) => dispatch(action.getUploadSpeedChart(params)),
  };
};

interface StateMachines<T> extends State<T, EStatusMachine> {
  machines?: Pagination<MachineModel>;
  machineConfig?: MachineConfigModel[];
  deviceLogs?: DeviceLogs[];
  deviceFileLogs?: DeviceFileLogs[];
  metadata?: Pagination<Metadata>;
  errorList?: Pagination<ErrorList>;
  imagesTest?: Pagination<ImagesTest>;
  deviceMainConfig?: Pagination<DeviceMainConfig>;
  deviceCommands?: DeviceCommandModel[];
  buttonCommands?: ButtonCommandResponse;
  infoUpload?: Attachment[];
  listUpload?: Record<string, Attachment[]>;
  showList?: Record<string, boolean>;
  description?: Record<string, string>;
  batteryChart?: any;
  temperatureChart?: any;
  humidityChart?: any;
  uploadSpeedChart?: any;
  currentTabDetail?: string;
}

export class DeviceCommandModel extends CommonEntity {
  constructor(
    public id: string,
    public deviceId: string,
    public name: string,
    public value: string,
    public service: string,
    public createdOnDate: string,
    public deliverDate?: string | null,
    public isDelivered?: boolean,
    public result?: string,
    public isNotifyToBroker?: boolean,
    public type?: string,
  ) {
    super();
  }
}

export class ButtonCommandModel {
  constructor(
    public type: string,
    public typeCommand: string,
    public classCommand: string,
    public nameCommand: string,
    public valueCommand: string,
    public buttonLabelCommand: string,
    public labelCommand: string,
  ) {}
}

export interface ButtonCommandResponse {
  analogType: ButtonCommandModel[];
  doubleClickType: ButtonCommandModel[];
  logType: ButtonCommandModel[];
  updateType: ButtonCommandModel[];
}

export class MachineModel extends CommonEntity {
  constructor(
    public id: string,
    public deviceId?: string,
    public name?: string,
    public deviceSerialNo?: string,
    public status?: number,
    public heartbeatStatus?: number,
    public batteryVolt?: number,
    public freeSpace?: number,
    public totalSpace?: number,
    public uploadSpeed?: number,
    public version?: string,
    public lastUpdate?: string,
    public lastUpdatedMinsAgo?: number,
    public lastCapturedTime?: string,
    public lastCapturedMinsAgo?: number,
    public lastCapturedImage?: string,
    public lastUploadedTime?: string,
    public lastUploadedMinsAgo?: number,
    public lastUploadedImage?: string,
    public lastUploadedImageAverageSpeed?: number,
    public wakeTime?: string,
    public totalCommand?: number,
    public isCaptureThreadAlive?: boolean,
    public isMaintainThreadAlive?: boolean,
    public isUploadThreadAlive?: boolean,
    public lastCamErrorState?: string,
    public lastDoorErrorState?: string,
    public networkMode?: string,
    public captureMode?: string,
    public imageEv?: string,
    public imageIso?: string,
    public imageUrl?: string,
    public imageTestUrl?: string,
    public backupVersion?: string,
    public folderId?: string,
    public group?: string,
    public groupId?: string,
    public hasAttachment?: boolean,
    public hasMqtt?: boolean,
    public isDeleted?: boolean,
    public meta?: string,
    public searchString?: string,
    public createdDate?: string,
  ) {
    super();
  }
}

export class MachineConfigModel extends CommonEntity {
  constructor(
    public id: string,
    public deviceId: string,
    public deviceName: string,
    public name: string,
    public value: string,
    public createdOnDate?: string,
    public lastModifiedOnDate?: string,
  ) {
    super();
  }
}

export type DeviceLogs = {
  content: string;
  createdDate: string;
  clockDiff: number;
};

export type DeviceFileLogs = {
  content: string;
  createdDate: string;
  clockDiff: number;
  logType: 'Main' | 'Backup';
};

type Metadata = {
  id: string;
  name: string;
  value: string;
  lastUpdate: string;
};

type ErrorList = {
  id: string;
  errorCode: string;
  detectTimes: number;
  errorDescription: string;
  lastDetectOnDate: number;
};

type ImagesTest = {
  id: string;
  deviceId: string;
  filename: string;
  url: string;
  type: string;
  createdOnDate: string;
};

type DeviceMainConfig = {
  id: string;
  deviceId: string;
  name: string;
  readonly: number;
  type: string;
  current: {
    value: string;
    label: string;
  };
  choices: {
    value: string;
    label: string;
  }[];
  lastUpdateCurrentValue: string;
  createdDate: string;
};

export enum EStatusMachine {
  getMachinesPending = 'getMachinesPending',
  getMachinesFulfilled = 'getMachinesFulfilled',
  getMachinesRejected = 'getMachinesRejected',
  getMachineConfigPending = 'getMachineConfigPending',
  getMachineConfigFulfilled = 'getMachineConfigFulfilled',
  getMachineConfigRejected = 'getMachineConfigRejected',

  getDeviceLogsPending = 'getDeviceLogsPending',
  getDeviceLogsFulfilled = 'getDeviceLogsFulfilled',
  getDeviceLogsRejected = 'getDeviceLogsRejected',

  getDeviceFileLogsPending = 'getDeviceFileLogsPending',
  getDeviceFileLogsFulfilled = 'getDeviceFileLogsFulfilled',
  getDeviceFileLogsRejected = 'getDeviceFileLogsRejected',

  getByNamePending = 'getByNamePending',
  getByNameFulfilled = 'getByNameFulfilled',
  getByNameRejected = 'getByNameRejected',

  getMetadataPending = 'getMetadataPending',
  getMetadataFulfilled = 'getMetadataFulfilled',
  getMetadataRejected = 'getMetadataRejected',

  getErrorListPending = 'getErrorListPending',
  getErrorListFulfilled = 'getErrorListFulfilled',
  getErrorListRejected = 'getErrorListRejected',

  getImagesTestPending = 'getImagesTestPending',
  getImagesTestFulfilled = 'getImagesTestFulfilled',
  getImagesTestRejected = 'getImagesTestRejected',

  getDeviceMainConfigPending = 'getDeviceMainConfigPending',
  getDeviceMainConfigFulfilled = 'getDeviceMainConfigFulfilled',
  getDeviceMainConfigRejected = 'getDeviceMainConfigRejected',

  getByIdToAttachmentsTemplatePending = 'getByIdToAttachmentsTemplatePending',
  getByIdToAttachmentsTemplateFulfilled = 'getByIdToAttachmentsTemplateFulfilled',
  getByIdToAttachmentsTemplateRejected = 'getByIdToAttachmentsTemplateRejected',

  getAttachmentsPending = 'getAttachmentsPending',
  getAttachmentsFulfilled = 'getAttachmentsFulfilled',
  getAttachmentsRejected = 'getAttachmentsRejected',

  createAttachmentsTemplatePending = 'createAttachmentsTemplatePending',
  createAttachmentsTemplateFulfilled = 'createAttachmentsTemplateFulfilled',
  createAttachmentsTemplateRejected = 'createAttachmentsTemplateRejected',

  deleteImagePending = 'deleteImagePending',
  deleteImageFulfilled = 'deleteImageFulfilled',
  deleteImageRejected = 'deleteImageRejected',

  postDeviceCommandPending = 'postDeviceCommandPending',
  postDeviceCommandFulfilled = 'postDeviceCommandFulfilled',
  postDeviceCommandRejected = 'postDeviceCommandRejected',

  changeConfigPending = 'changeConfigPending',
  changeConfigFulfilled = 'changeConfigFulfilled',
  changeConfigRejected = 'changeConfigRejected',

  deleteDeviceErrorPending = 'deleteDeviceErrorPending',
  deleteDeviceErrorFulfilled = 'deleteDeviceErrorFulfilled',
  deleteDeviceErrorRejected = 'deleteDeviceErrorRejected',

  getDeviceCommandPending = 'getDeviceCommandPending',
  getDeviceCommandFulfilled = 'getDeviceCommandFulfilled',
  getDeviceCommandRejected = 'getDeviceCommandRejected',

  getButtonCommandPending = 'getButtonCommandPending',
  getButtonCommandFulfilled = 'getButtonCommandFulfilled',
  getButtonCommandRejected = 'getButtonCommandRejected',

  deleteDeviceCommandPending = 'deleteDeviceCommandPending',
  deleteDeviceCommandFulfilled = 'deleteDeviceCommandFulfilled',
  deleteDeviceCommandRejected = 'deleteDeviceCommandRejected',

  getBatteryChartPending = 'getBatteryChartPending',
  getBatteryChartFulfilled = 'getBatteryChartFulfilled',
  getBatteryChartRejected = 'getBatteryChartRejected',

  getTemperatureChartPending = 'getTemperatureChartPending',
  getTemperatureChartFulfilled = 'getTemperatureChartFulfilled',
  getTemperatureChartRejected = 'getTemperatureChartRejected',

  getHumidityChartPending = 'getHumidityChartPending',
  getHumidityChartFulfilled = 'getHumidityChartFulfilled',
  getHumidityChartRejected = 'getHumidityChartRejected',

  getUploadSpeedChartPending = 'getUploadSpeedChartPending',
  getUploadSpeedChartFulfilled = 'getUploadSpeedChartFulfilled',
  getUploadSpeedChartRejected = 'getUploadSpeedChartRejected',
}
