import { DeleteOutlined, EditOutlined, PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import { SubHeader } from '@layouts/admin';
import { EStatusState, QueryParams } from '@models';
import { InputSearch } from '@pages/shared-directory/search-widget';
import UserGroupEditForm from '@pages/user/user-group-management/edit.drawer';
import { RolesFacade, RolesModel } from '@store';
import { Button, Card, Flex, Modal, Space, Table, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useSearchParams } from 'react-router-dom';

const Page: React.FC = () => {
  const [modalApi, contextModalApi] = Modal.useModal();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const rolesFacade = RolesFacade();

  const {
    page,
    size,
    filter = '{}',
    sort = '',
    id = '',
  } = {
    ...Object.fromEntries(searchParams),
    page: Number(searchParams.get('page') || 1),
    size: Number(searchParams.get('size') || 20),
  };

  const parsedFilter = JSON.parse(filter);

  useEffect(() => {
    onChangeDataTable({});
  }, []);

  useEffect(() => {
    switch (rolesFacade.status) {
      case EStatusState.postFulfilled:
      case EStatusState.putFulfilled:
      case EStatusState.deleteFulfilled:
        onChangeDataTable({});
        rolesFacade.set({ isVisible: false });
        break;
    }
  }, [rolesFacade.status]);

  const datasource: RolesModel[] =
    rolesFacade.pagination?.content.map((items, index) => ({
      stt: (Number(rolesFacade.pagination?.page ?? 0) - 1) * Number(rolesFacade.pagination?.size ?? 0) + index + 1,
      ...items,
    })) ?? [];

  const handleEdit = (record: RolesModel) => {
    if (record.id) {
      setSearchParams(
        {
          ...Object.fromEntries(searchParams),
          id: record.id,
        },
        { replace: true },
      );
      rolesFacade.set({ isEdit: true });
    }
  };
  const onChangeSearch = (value: string) => {
    if (value) {
      parsedFilter.fullTextSearch = value;
    } else {
      delete parsedFilter.fullTextSearch;
    }
    onChangeDataTable({
      query: {
        page: 1,
        size,
        filter: JSON.stringify({ ...parsedFilter }),
      },
    });
  };

  const handleDelete = (record: RolesModel) => {
    modalApi.confirm({
      title: 'Xác nhận xoá nhóm người dùng',
      content: `Nhóm người dùng ${record.code} sẽ bị xóa. Bạn có muốn tiếp tục?`,
      onOk: () => {
        record?.id && rolesFacade.delete(record.id);
      },
      onCancel: () => {},
      okText: 'Đồng ý',
      okButtonProps: { variant: 'outlined' },
      cancelText: 'Huỷ',
      cancelButtonProps: { danger: true },
    });
  };

  const onChangeDataTable = (props: { query?: QueryParams; setKeyState?: object }) => {
    if (!props.query) {
      props.query = {
        page,
        size,
        filter,
        sort,
        id,
      };
    }
    const fillQuery: QueryParams = { ...rolesFacade.query, ...props.query };
    for (const key in fillQuery) {
      if (!fillQuery[key as keyof QueryParams]) delete fillQuery[key as keyof QueryParams];
    }
    rolesFacade.get(fillQuery);
    navigate(
      { search: new URLSearchParams(fillQuery as unknown as Record<string, string>).toString() },
      { replace: true },
    );
    rolesFacade.set({ query: props.query, ...props.setKeyState });
  };

  const columns: ColumnsType<RolesModel> = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      align: 'center',
      width: 60,
    },
    {
      title: 'Mã nhóm người dùng',
      dataIndex: 'code',
      key: 'code',
      width: 200,
    },
    {
      title: 'Tên nhóm người dùng',
      dataIndex: 'name',
      key: 'name',
      width: 300,
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: 'Hành động',
      dataIndex: 'action',
      key: 'Action',
      align: 'center',
      width: 150,
      render: (_, record: RolesModel) => (
        <Space size={'small'}>
          <Tooltip title="Chỉnh sửa">
            <Button
              size="small"
              color="blue"
              variant="outlined"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              type={'link'}
            />
          </Tooltip>
          <Tooltip title="Xoá nhóm người dùng">
            <Button
              size="small"
              color="danger"
              variant="outlined"
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <>
      {contextModalApi}
      <SubHeader />
      <div className="m-6 intro-x">
        <Card size="small" variant="borderless">
          <Table
            title={() => (
              <Flex className="!mb-4" gap={20}>
                <InputSearch
                  defaultValue={parsedFilter?.fullTextSearch}
                  callback={onChangeSearch}
                  placeholder={'Tìm kiếm theo mã nhóm người dùng, tên nhóm người dùng, mô tả'}
                />
                <Space className="float-right">
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() =>
                      onChangeDataTable({
                        query: {
                          page: 1,
                          size,
                          filter: JSON.stringify({ ...parsedFilter }),
                        },
                      })
                    }
                  >
                    Tải lại
                  </Button>
                  <Button type="primary" icon={<PlusOutlined />} onClick={() => rolesFacade.set({ isCreate: true })}>
                    Tạo nhóm người dùng
                  </Button>
                </Space>
              </Flex>
            )}
            loading={rolesFacade.isLoading}
            size="small"
            bordered
            dataSource={datasource}
            columns={columns}
            rowKey="id"
            scroll={{ x: 'max-content', y: 'calc(100vh - 450px)' }}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: [10, 20, 50, 100],
              total: rolesFacade.pagination?.totalElements,
              current: page,
              pageSize: size,
              showTotal: (total, range) => `Từ ${range[0]} đến ${range[1]} trên tổng ${total}`,
              onChange: (page, size) => {
                onChangeDataTable({
                  query: {
                    page,
                    size,
                    filter: JSON.stringify({ ...parsedFilter }),
                  },
                });
              },
            }}
          />
        </Card>
      </div>
      <UserGroupEditForm />
    </>
  );
};

export default Page;
