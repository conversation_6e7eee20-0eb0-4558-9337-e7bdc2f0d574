import React, { useEffect, useRef, useCallback, useState, forwardRef, useImperativeHandle } from 'react';
import {
  Environment,
  GpsComponentProps,
  Scene,
  GpsStatus,
  GpsLoadingState,
  GpsComponentRef,
  GpsError
} from '../../utils/types/gps.types';

// Default environment configuration
const defaultEnvironment: Environment = {
  adminApiUrl: 'https://api-monitor.autotimelapse.com',
  production: false,
  userData: '#?EzQf#THc%jJX84H6ph&7nx_Q*eg_',
  gpsUrl: 'https://gpsdev.geneat.pro'
};

/**
 * GPS Component - React TypeScript version
 *
 * This component renders an iframe that displays GPS tracking information
 * from the configured GPS URL. It manages the iframe lifecycle and handles
 * layout adjustments similar to the original Angular component.
 */
const GpsComponent = forwardRef<GpsComponentRef, GpsComponentProps>(({
  environment = defaultEnvironment,
  className = '',
  onLoad,
  onError,
  title = 'GPS Tracking',
  allowFullScreen = true,
  loading = 'lazy'
}, ref) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [status, setStatus] = useState<GpsStatus>({
    state: 'idle',
    isLoading: false,
    hasError: false
  });

  /**
   * Update GPS status
   */
  const updateStatus = useCallback((newState: GpsLoadingState, error?: GpsError) => {
    setStatus(prev => ({
      ...prev,
      state: newState,
      isLoading: newState === 'loading',
      hasError: newState === 'error',
      error,
      lastLoaded: newState === 'loaded' ? new Date() : prev.lastLoaded,
      currentUrl: environment.gpsUrl
    }));
  }, [environment.gpsUrl]);

  /**
   * Handle iframe load event
   */
  const handleIframeLoad = useCallback(() => {
    console.log('GPS iframe loaded successfully');
    updateStatus('loaded');
    onLoad?.();
  }, [onLoad, updateStatus]);

  /**
   * Handle iframe error event
   */
  const handleIframeError = useCallback(() => {
    const error: GpsError = {
      message: 'Failed to load GPS iframe',
      timestamp: new Date()
    };
    console.error('GPS iframe failed to load:', error);
    updateStatus('error', error);
    onError?.(new Error(error.message));
  }, [onError, updateStatus]);

  /**
   * Reload the GPS iframe
   */
  const reload = useCallback(() => {
    const iframe = iframeRef.current;
    if (iframe && environment.gpsUrl) {
      updateStatus('loading');
      iframe.src = environment.gpsUrl;
    }
  }, [environment.gpsUrl, updateStatus]);

  /**
   * Update GPS URL
   */
  const updateUrl = useCallback((url: string) => {
    const iframe = iframeRef.current;
    if (iframe) {
      updateStatus('loading');
      iframe.src = url;
    }
  }, [updateStatus]);

  /**
   * Expose methods via ref
   */
  useImperativeHandle(ref, () => ({
    reload,
    getStatus: () => status,
    updateUrl,
    getIframe: () => iframeRef.current
  }), [reload, status, updateUrl]);

  /**
   * Initialize the GPS iframe (similar to Angular ngOnInit)
   */
  useEffect(() => {
    const iframe = iframeRef.current;

    if (iframe && environment.gpsUrl) {
      // Set loading state
      updateStatus('loading');

      // Set the iframe source to the GPS URL
      iframe.src = environment.gpsUrl;

      // Add event listeners
      iframe.addEventListener('load', handleIframeLoad);
      iframe.addEventListener('error', handleIframeError);

      console.log('GPS Component initialized with URL:', environment.gpsUrl);
    }

    // Cleanup function (similar to Angular ngOnDestroy)
    return () => {
      if (iframe) {
        iframe.removeEventListener('load', handleIframeLoad);
        iframe.removeEventListener('error', handleIframeError);
      }
    };
  }, [environment.gpsUrl, handleIframeLoad, handleIframeError, updateStatus]);

  return (
    <div className={`gps-component ${className} h-full mt-16`}>
      <iframe
        ref={iframeRef}
        id="gps-container"
        title={title}
        className="w-full h-full border-0"
        allowFullScreen={allowFullScreen}
        loading={loading}
      />
    </div>
  );
});

// Add display name for debugging
GpsComponent.displayName = 'GpsComponent';

export default GpsComponent;

// Export types for external use
export type { GpsComponentProps, Environment, Scene };
