# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/dist
/build

# misc
.env
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.vite

npm-debug.log*
yarn-debug.log*
yarn-error.log*
test/result
/package-lock.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.vite/*

dev-dist/*