import { useState, useEffect } from 'react';
import { Image, Spin, Typography } from 'antd';

export default function LoadingScreen() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const handleLoaded = () => setIsLoading(false);

    if (document.readyState === 'complete') {
      handleLoaded();
    } else {
      window.addEventListener('DOMContentLoaded', handleLoaded);
      return () => window.removeEventListener('DOMContentLoaded', handleLoaded);
    }
  }, []);

  return isLoading ? (
    <Spin
      size="large"
      indicator={
        <Image className="!w-24 !h-24" preview={false} src={'/assets/gifs/loading-screen.gif'} alt="loading-screen" />
      }
      fullscreen
      tip={<Typography.Text strong>Đang lấy dữ liệu...</Typography.Text>}
    />
  ) : null;
}
