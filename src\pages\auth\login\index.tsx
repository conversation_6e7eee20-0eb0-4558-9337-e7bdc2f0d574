import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { EStatusGlobal, GlobalFacade, LoginModel } from '@store';
import { routerLinks } from '@utils';
import { Button, Checkbox, Flex, Form, Image, Input, Typography } from 'antd';
import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { Link } from 'react-router-dom';
const Page = () => {
  const navigate = useNavigate();
  const globalFacade = GlobalFacade();
  const { status, user, login, isLoading } = globalFacade;
  const [loginForm] = Form.useForm();
  useEffect(() => {
    if (status === EStatusGlobal.loginFulfilled && user && Object.keys(user).length > 0) {
      navigate('/machines');
    }
  }, [status]);

  const onFinish = (values: LoginModel) => {
    login(values);
  };

  return (
    <>
      <div className="!w-full md:!w-md">
        <Flex align="center" justify="center" gap={10} vertical>
          <Image preview={false} alt="logo-auth" src="/assets/images/logo1.png" />
          <Typography.Title className="!mb-5" level={4}>
            Đăng nhập tài khoản
          </Typography.Title>
        </Flex>
        <Form autoComplete="off" form={loginForm} onFinish={onFinish}>
          <Form.Item name="loginName" rules={[{ required: true, message: 'Tài khoản là bắt buộc!' }]}>
            <Input size="large" prefix={<UserOutlined />} placeholder={'Tài khoản'} />
          </Form.Item>
          <Form.Item className="!mb-3" name="password" rules={[{ required: true, message: 'Mật khẩu là bắt buộc!' }]}>
            <Input.Password size="large" prefix={<LockOutlined />} placeholder={'Mật khẩu'} />
          </Form.Item>
          <Form.Item className="!mb-2" name="rememberMe" valuePropName="checked">
            <Checkbox>Nhớ mật khẩu</Checkbox>
          </Form.Item>
          <Form.Item className="!mb-2">
            <Button loading={isLoading} size="large" block type="primary" htmlType="submit">
              Đăng nhập
            </Button>
          </Form.Item>
          <Form.Item>
            Bạn chưa có tài khoản?{' '}
            <Link to={`${routerLinks('Register')}`} className="!text-primary-gold !underline">
              Đăng ký ngay!
            </Link>
          </Form.Item>
        </Form>
      </div>
    </>
  );
};

export default Page;
