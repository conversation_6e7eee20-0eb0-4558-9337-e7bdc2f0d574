/* eslint-disable @typescript-eslint/no-unused-vars */
/// <reference types="vite/client" />
/// <reference types="vite-plugin-svgr/client" />

declare module '*.svg' {
  import * as React from 'react';
  export const ReactComponent: React.FunctionComponent<React.SVGProps<SVGSVGElement> & { title?: string }>;
  const src: string;
  export default src;
}

interface ImportMetaEnv {
  readonly VITE_URL_SOCKET: string;
  readonly VITE_URL_API: string;
  readonly VITE_URL_LANGUAGES: string;
  readonly VITE_URL_LANGUAGE: string;
  readonly VITE_WEB_URL: string;
  readonly VITE_URL_LOCAL: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
declare const echarts: echarts;
declare const Inputmask: inputmask;
