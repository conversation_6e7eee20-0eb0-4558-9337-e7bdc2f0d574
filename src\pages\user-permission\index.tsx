import { useEffect, useState } from 'react';
import { Table, Button, Input, Space, Switch, message } from 'antd';
import { SearchOutlined, UserOutlined, SettingOutlined } from '@ant-design/icons';
import { UserPermissionsFacade } from '@store';

export default function UserPermission() {
  const userPermissions = UserPermissionsFacade();
  const [searchText, setSearchText] = useState('');
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);

  // Load users on component mount
  useEffect(() => {
    userPermissions.getUsers({
      page: 0,
      size: 10,
      filter: '',
      fullTextSearch: searchText,
    });
  }, [searchText]);

  // Load user devices when a user is selected
  useEffect(() => {
    if (selectedUserId) {
      userPermissions.getUserDevices({
        userId: selectedUserId,
        page: 0,
        size: 10,
        filter: '',
        fullTextSearch: '',
      });
    }
  }, [selectedUserId]);

  const handlePermissionChange = async (
    userId: string,
    deviceId: string,
    permissionType: 'read' | 'write',
    hasPermission: boolean
  ) => {
    try {
      if (permissionType === 'read') {
        if (hasPermission) {
          await userPermissions.removeReadPermission({ userId, deviceId });
        } else {
          await userPermissions.addReadPermission({ userId, deviceId });
        }
      } else {
        if (hasPermission) {
          await userPermissions.removeWritePermission({ userId, deviceId });
        } else {
          await userPermissions.addWritePermission({ userId, deviceId });
        }
      }

      // Reload user devices to reflect changes
      userPermissions.getUserDevices({
        userId,
        page: 0,
        size: 10,
        filter: '',
        fullTextSearch: '',
      });
    } catch (error) {
      message.error('Failed to update permission');
    }
  };

  const userColumns = [
    {
      title: 'User Name',
      dataIndex: 'userName',
      key: 'userName',
      render: (text: string) => (
        <Space>
          <UserOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: 'Full Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Active',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <span className={isActive ? 'text-green-600' : 'text-red-600'}>
          {isActive ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: any) => (
        <Button
          type="primary"
          icon={<SettingOutlined />}
          onClick={() => setSelectedUserId(record.id)}
          disabled={selectedUserId === record.id}
        >
          {selectedUserId === record.id ? 'Selected' : 'Manage Permissions'}
        </Button>
      ),
    },
  ];

  const deviceColumns = [
    {
      title: 'Device Name',
      dataIndex: 'deviceName',
      key: 'deviceName',
    },
    {
      title: 'Device ID',
      dataIndex: 'deviceId',
      key: 'deviceId',
    },
    {
      title: 'Read Permission',
      dataIndex: 'hasReadPermission',
      key: 'hasReadPermission',
      render: (hasPermission: boolean, record: any) => (
        <Switch
          checked={hasPermission}
          onChange={() => handlePermissionChange(selectedUserId!, record.deviceId, 'read', hasPermission)}
          loading={userPermissions.isLoading}
        />
      ),
    },
    {
      title: 'Write Permission',
      dataIndex: 'hasWritePermission',
      key: 'hasWritePermission',
      render: (hasPermission: boolean, record: any) => (
        <Switch
          checked={hasPermission}
          onChange={() => handlePermissionChange(selectedUserId!, record.deviceId, 'write', hasPermission)}
          loading={userPermissions.isLoading}
        />
      ),
    },
    {
      title: 'Assigned Date',
      dataIndex: 'assignedDate',
      key: 'assignedDate',
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '-',
    },
  ];

  return (
    <div className="p-4 space-y-6 mt-16 lg:max-w-[1500px] lg:mx-auto">
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">User Management</h2>

        <div className="mb-4">
          <Input
            placeholder="Search users..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="max-w-md"
          />
        </div>

        <Table
          dataSource={userPermissions.users?.content || []}
          columns={userColumns}
          loading={userPermissions.isLoading}
          pagination={{
            current: (userPermissions.users?.page || 0) + 1,
            pageSize: userPermissions.users?.size || 10,
            total: userPermissions.users?.totalElements || 0,
            onChange: (page, size) => {
              userPermissions.getUsers({
                page: page - 1,
                size: size || 10,
                filter: '',
                fullTextSearch: searchText,
              });
            },
          }}
          rowKey="id"
        />
      </div>

      {selectedUserId && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Device Permissions</h2>

          <Table
            dataSource={userPermissions.userDevices?.content || []}
            columns={deviceColumns}
            loading={userPermissions.isLoading}
            pagination={{
              current: (userPermissions.userDevices?.page || 0) + 1,
              pageSize: userPermissions.userDevices?.size || 10,
              total: userPermissions.userDevices?.totalElements || 0,
              onChange: (page, size) => {
                userPermissions.getUserDevices({
                  userId: selectedUserId,
                  page: page - 1,
                  size: size || 10,
                  filter: '',
                  fullTextSearch: '',
                });
              },
            }}
            rowKey="id"
          />
        </div>
      )}
    </div>
  );
}
