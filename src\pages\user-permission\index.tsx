import { Table } from "antd";

export default function UserPermission() {
  const dataSource = [
    {
      key: '1',
      name: '<PERSON>',
      age: 32,
      address: '10 Downing Street',
    },
    {
      key: '2',
      name: '<PERSON>',
      age: 42,
      address: '10 Downing Street',
    },
  ];

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
    },
  ];
  return (
    <>
      <div className="p-4 space-y-4 mt-16 lg:max-w-[1500px] lg:mx-auto">
        <Table dataSource={dataSource} columns={columns} />
      </div>
    </>
  );
}
