import { useEffect, useState } from 'react';
import { Table, Button, Input, Space, Select, Tag, Modal, message, List, Avatar } from 'antd';
import {
  SearchOutlined,
  UserOutlined,
  SettingOutlined,
  LockOutlined,
  UnlockOutlined,
  ExclamationCircleOutlined,
  MobileOutlined,
  DesktopOutlined,
} from '@ant-design/icons';
import { UserPermissionsFacade } from '@store';
import { useIsMobile } from 'src/utils/hooks/useIsMobile';

const { Option } = Select;
const { confirm } = Modal;

export default function UserPermission() {
  const userPermissions = UserPermissionsFacade();
  const [searchText, setSearchText] = useState('');
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [selectedUserName, setSelectedUserName] = useState<string>('');
  const [isDeviceModalVisible, setIsDeviceModalVisible] = useState(false);
  const [deviceSearchText, setDeviceSearchText] = useState('');
  const isMobile = useIsMobile();

  // Load users on component mount
  useEffect(() => {
    userPermissions.getUsers({
      page: 1,
      size: 10,
      filter: '{}',
      fullTextSearch: searchText,
    });
  }, [searchText]);

  // Load user devices when a user is selected
  useEffect(() => {
    if (selectedUserId) {
      userPermissions.getUserDevices({
        userId: selectedUserId,
        page: 1,
        size: 20,
        filter: '{}',
        fullTextSearch: '',
      });
    }
  }, [selectedUserId]);

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return 'red';
      case 'ADMIN':
        return 'orange';
      case 'USER':
        return 'blue';
      case 'CUSTOMER':
        return 'green';
      case 'TECHNICAL':
        return 'purple';
      default:
        return 'default';
    }
  };

  const handleManageDevices = (userId: string, userName: string) => {
    setSelectedUserId(userId);
    setSelectedUserName(userName);

    if (isMobile) {
      setIsDeviceModalVisible(true);
    }
  };

  const handleLockUser = (userId: string, userName: string, isLocked: boolean) => {
    const actionText = isLocked ? 'unlock' : 'lock';

    confirm({
      title: `Bạn có chắc muốn ${actionText} người dùng này?`,
      icon: <ExclamationCircleOutlined />,
      content: `User: ${userName}`,
      okText: `Có, ${actionText}`,
      okType: isLocked ? 'primary' : 'danger',
      cancelText: 'Huỷ',
      onOk() {
        if (isLocked) {
          userPermissions.unlockUser({ userId });
        } else {
          userPermissions.lockUser({ userId });
        }

        // Reload users to reflect changes
        setTimeout(() => {
          userPermissions.getUsers({
            page: 1,
            size: 10,
            filter: '{}',
            fullTextSearch: searchText,
          });
        }, 1000);
      },
    });
  };

  const handlePermissionChange = (deviceId: string, newPermission: string) => {
    if (!selectedUserId) return;

    userPermissions.updateUserDevicePermission({
      userId: selectedUserId,
      deviceId,
      permission: newPermission,
    });

    // Reload user devices to reflect changes
    setTimeout(() => {
      userPermissions.getUserDevices({
        userId: selectedUserId,
        page: 1,
        size: 20,
        filter: '{}',
        fullTextSearch: '',
      });
    }, 1000);
  };

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'r':
        return 'blue';
      case 'w':
        return 'orange';
      case 'rw':
        return 'green';
      default:
        return 'default';
    }
  };

  const getPermissionText = (permission: string) => {
    switch (permission) {
      case 'r':
        return 'Read Only';
      case 'w':
        return 'Write Only';
      case 'rw':
        return 'Read & Write';
      default:
        return 'No Access';
    }
  };

  const userColumns = [
    {
      title: 'Login Name',
      dataIndex: 'loginName',
      key: 'loginName',
      render: (text: string) => (
        <Space>
          <UserOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: 'Display Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Roles',
      dataIndex: 'roles',
      key: 'roles',
      render: (roles: string) => <Tag color={getRoleColor(roles)}>{roles}</Tag>,
    },
    {
      title: 'Level',
      dataIndex: 'level',
      key: 'level',
    },
    {
      title: 'Status',
      dataIndex: 'isLocked',
      key: 'isLocked',
      render: (isLocked: boolean, record: any) => (
        <Space>
          <Tag color={isLocked ? 'red' : 'green'}>{isLocked ? 'Locked' : 'Active'}</Tag>
          <Tag color={record.isActivated ? 'green' : 'orange'}>
            {record.isActivated ? 'Activated' : 'Not Activated'}
          </Tag>
        </Space>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: any) => (
        <Space>
          <Button
            type={record.isLocked ? 'primary' : 'default'}
            danger={!record.isLocked}
            icon={record.isLocked ? <UnlockOutlined /> : <LockOutlined />}
            onClick={() => handleLockUser(record.id, record.loginName, record.isLocked)}
            loading={userPermissions.isLoading}
          >
            {record.isLocked ? 'Unlock' : 'Lock'}
          </Button>
          <Button
            type="primary"
            icon={isMobile ? <MobileOutlined /> : <SettingOutlined />}
            onClick={() => handleManageDevices(record.id, record.loginName)}
            disabled={selectedUserId === record.id && !isMobile}
          >
            {selectedUserId === record.id && !isMobile ? 'Selected' : 'Manage Devices'}
          </Button>
        </Space>
      ),
    },
  ];

  const deviceColumns = [
    {
      title: 'Device Name',
      dataIndex: 'deviceName',
      key: 'deviceName',
      width: '40%',
    },
    {
      title: 'Current Permission',
      dataIndex: 'permission',
      key: 'permission',
      width: '15%',
      render: (permission: string) => <Tag color={getPermissionColor(permission)}>{getPermissionText(permission)}</Tag>,
    },
    {
      title: 'Change Permission',
      key: 'changePermission',
      width: '15%',
      render: (record: any) => (
        <Select
          value={record.permission}
          onChange={(value) => handlePermissionChange(record.deviceId, value)}
          loading={userPermissions.isLoading}
          style={{ width: '100%' }}
        >
          <Option value="">No Access</Option>
          <Option value="r">Read Only</Option>
          <Option value="w">Write Only</Option>
          <Option value="rw">Read & Write</Option>
        </Select>
      ),
    },
  ];

  return (
    <div className="p-4 space-y-6 mt-16 lg:max-w-[1500px] lg:mx-auto">
      <div className="mb-4">
        <Input
          placeholder="Search users by login name or display name..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className="max-w-md"
          allowClear
        />
      </div>

      {!isMobile ? (
        <>
          <Table
            dataSource={userPermissions.users?.content || []}
            columns={userColumns}
            loading={userPermissions.isLoading}
            pagination={{
              current: userPermissions.users?.page || 1,
              pageSize: userPermissions.users?.size || 10,
              total: userPermissions.users?.totalElements || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} users`,
              onChange: (page, size) => {
                userPermissions.getUsers({
                  page: page - 1,
                  size: size || 10,
                  filter: '{}',
                  fullTextSearch: searchText,
                });
              },
            }}
            rowKey="id"
            scroll={{ x: 800 }}
          />

          {selectedUserId && (
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-semibold text-gray-800">
                  Device Permissions for: <span className="text-blue-600">{selectedUserName}</span>
                </h2>
                <div className="text-sm text-gray-500">
                  Total Devices: {userPermissions.userDevices?.totalElements || 0}
                </div>
              </div>

              <div className="mb-4">
                <Input
                  placeholder="Search devices by name or ID..."
                  prefix={<SearchOutlined />}
                  value={deviceSearchText}
                  onChange={(e) => setDeviceSearchText(e.target.value)}
                  className="max-w-md mb-4"
                  allowClear
                />
              </div>

              <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">Permission Types:</h4>
                <div className="flex flex-wrap gap-2">
                  <Tag color="default">No Access - Cannot read or write</Tag>
                  <Tag color="blue">Read Only - Can view device data</Tag>
                  <Tag color="orange">Write Only - Can send commands</Tag>
                  <Tag color="green">Read & Write - Full access</Tag>
                </div>
              </div>

              <Table
                dataSource={(userPermissions.userDevices?.content || []).filter(
                  (device) =>
                    device.deviceName?.toLowerCase().includes(deviceSearchText.toLowerCase()) ||
                    device.deviceId?.toLowerCase().includes(deviceSearchText.toLowerCase()),
                )}
                columns={deviceColumns}
                loading={userPermissions.isLoading}
                pagination={{
                  current: userPermissions.userDevices?.page || 1,
                  pageSize: userPermissions.userDevices?.size || 20,
                  total: userPermissions.userDevices?.totalElements || 0,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} devices`,
                  onChange: (page, size) => {
                    userPermissions.getUserDevices({
                      userId: selectedUserId,
                      page: page - 1,
                      size: size || 20,
                      filter: '{}',
                      fullTextSearch: deviceSearchText,
                    });
                  },
                }}
                rowKey={(record) => `${record.userId}-${record.deviceId}`}
                scroll={{ x: 800 }}
              />
            </div>
          )}
        </>
      ) : (
        <>
          <List
            itemLayout="horizontal"
            dataSource={userPermissions.users?.content || []}
            renderItem={(item) => (
              <List.Item>
                <List.Item.Meta
                  avatar={<Avatar icon={<UserOutlined />} />}
                  title={
                    <div className="flex flex-col space-y-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-bold text-sm">{item.name}</span>
                        <Tag color={getRoleColor(item.roles || '')}>
                          <div className="text-[9px]">{item.roles}</div>
                        </Tag>
                        {item.isLocked && <Tag color={'orange'}> <div className='text-[9px]'>{'Đang khoá'}</div></Tag>}
                      </div>
                      <div className="text-xs text-gray-500">@{item.loginName}</div>
                    </div>
                  }
                  description={
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Tag color="blue">
                        <div className="text-[9px]">Level {item.level}</div>
                      </Tag>
                    </div>
                  }
                />
                <div className="flex flex-col h-14 justify-between">
                  <Button
                    key="lock"
                    type={item.isLocked ? 'primary' : 'default'}
                    danger={!item.isLocked}
                    icon={item.isLocked ? <UnlockOutlined className="text-xs" /> : <LockOutlined className="text-xs" />}
                    onClick={() => handleLockUser(item.id, item.loginName || '', item.isLocked || false)}
                    loading={userPermissions.isLoading}
                    size="small"
                  >
                    <div className="text-xs">{item.isLocked ? 'Mở khoá' : 'Khoá'}</div>
                  </Button>
                  <Button
                    key="manage"
                    type="primary"
                    icon={<SettingOutlined className="text-xs" />}
                    onClick={() => handleManageDevices(item.id, item.loginName || '')}
                    size="small"
                  >
                    <div className="text-xs">Phân quyền</div>
                  </Button>
                </div>
              </List.Item>
            )}
          />
        </>
      )}

      {/* Mobile Device Permission Modal */}
      <Modal
        title={`Device Permissions - ${selectedUserName}`}
        open={isDeviceModalVisible}
        onCancel={() => setIsDeviceModalVisible(false)}
        footer={null}
        width="90%"
      >
        <div className="space-y-4">
          <Input
            placeholder="Search devices by name or ID..."
            prefix={<SearchOutlined />}
            value={deviceSearchText}
            onChange={(e) => setDeviceSearchText(e.target.value)}
            allowClear
          />

          <div className="p-3 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2 text-sm">Permission Types:</h4>
            <div className="flex flex-wrap gap-1">
              <Tag color="default">No Access</Tag>
              <Tag color="blue">Read Only</Tag>
              <Tag color="orange">Write Only</Tag>
              <Tag color="green">Read & Write</Tag>
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto">
            {(userPermissions.userDevices?.content || [])
              .filter(
                (device) =>
                  device.deviceName?.toLowerCase().includes(deviceSearchText.toLowerCase()) ||
                  device.deviceId?.toLowerCase().includes(deviceSearchText.toLowerCase()),
              )
              .map((device) => (
                <div key={`${device.userId}-${device.deviceId}`} className="border rounded-lg p-3 mb-3 bg-white">
                  <div className="font-medium text-sm mb-1 truncate">{device.deviceName}</div>
                  <div className="text-xs text-gray-500 mb-2 font-mono truncate">{device.deviceId}</div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-600">Current:</span>
                      <Tag color={getPermissionColor(device.permission)}>{getPermissionText(device.permission)}</Tag>
                    </div>

                    <Select
                      value={device.permission}
                      onChange={(value) => handlePermissionChange(device.deviceId, value)}
                      loading={userPermissions.isLoading}
                      size="small"
                      style={{ width: 120 }}
                    >
                      <Option value="">No Access</Option>
                      <Option value="r">Read Only</Option>
                      <Option value="w">Write Only</Option>
                      <Option value="rw">Read & Write</Option>
                    </Select>
                  </div>
                </div>
              ))}
          </div>

          {userPermissions.userDevices?.content?.length === 0 && (
            <div className="text-center py-8 text-gray-500">No devices found for this user</div>
          )}
        </div>
      </Modal>
    </div>
  );
}
