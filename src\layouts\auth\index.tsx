import React, { PropsWithChildren, useEffect } from 'react';

import { GlobalFacade } from '@store';

import './index.less';
import { Card, Spin } from 'antd';

const AuthLayout = ({ children }: PropsWithChildren) => {
  const globalFacade = GlobalFacade();

  useEffect(() => {
    globalFacade.logout();
  }, []);

  return (
    <div
      className="relative w-full h-screen bg-cover bg-center"
      style={{ backgroundImage: 'url(/assets/images/bg-3.jpg)' }}
    >
      <div className="absolute top-0 left-0 right-0 bottom-0 opacity-0 inset-0 bg-black flex items-center justify-center"></div>
      <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
        <Card className="!rounded-xl !px-6 bg-white !shadow-none !border-0">
          <Spin spinning={globalFacade.isLoading}>{children}</Spin>
        </Card>
      </div>
    </div>
  );
};
export default AuthLayout;
