import { createSlice } from '@reduxjs/toolkit';
import { useAppDispatch, useTypedSelector, Action, Slice, State, User, TypesCodeTypeManagement } from '@store';
import { CommonEntity, QueryParams } from '@models';

const name = 'CodeTypeManagement';
const action = {
  ...new Action<CodeTypeManagement, EStatusCodeTypeManagement>(name),
};
export const codeTypeManagementSlice = createSlice(
  new Slice<CodeTypeManagement, EStatusCodeTypeManagement>(action, { keepUnusedDataFor: 9999 }, (builder) => {
    builder;
  }),
);
export const CodeTypeManagementFacade = () => {
  const dispatch = useAppDispatch();
  return {
    ...useTypedSelector((state) => state[action.name] as StateCodeType<CodeTypeManagement>),
    set: (values: StateCodeType<CodeTypeManagement>) => dispatch(action.set(values)),
    get: (params: QueryParams) => dispatch(action.get(params)),
    getById: ({ id, keyState = 'isVisible' }: { id: string; keyState?: keyof StateCodeType<CodeTypeManagement> }) =>
      dispatch(action.getById({ id, keyState })),
    post: (values: CodeTypeManagement) => dispatch(action.post({ values })),
    put: (values: CodeTypeManagement) => dispatch(action.put({ values })),
    putDisable: (values: { id: string; disable: boolean }) => dispatch(action.putDisable(values)),
    delete: (id: string) => dispatch(action.delete({ id })),
  };
};
interface StateCodeType<T> extends State<T, EStatusCodeTypeManagement> {
  isEdit?: boolean;
  isVisibleForm?: boolean;
  isVisibleFormCOA?: boolean;
  id?: string;
}
export class CodeTypeManagement extends CommonEntity {
  constructor(
    public code?: string,
    public type?: string,
    public title?: string,
    public order?: number,
    public description?: string,
    public iconClass?: string,
    public codeTypeItems?: [
      {
        id?: string;
        lineNumber?: number;
        code?: string;
        title?: string;
        iconClass?: string;
        codeTypeId?: string;
        createdOnDate?: string;
      },
    ],
    public createdAt?: string,
    public updatedAt?: string,
    public item?: TypesCodeTypeManagement,
    public users?: User[],
  ) {
    super();
  }
}

export enum EStatusCodeTypeManagement {
  idle = 'idle',
}
