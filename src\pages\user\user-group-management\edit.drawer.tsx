import { UndoOutlined } from '@ant-design/icons';
import { EStatusState } from '@models';
import { RolesFacade, RolesModel } from '@store';
import { Button, Drawer, Form, Input, Space } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useSearchParams } from 'react-router-dom';

export default function UserGroupEditForm() {
  const rolesFacade = RolesFacade();
  const [roleForm] = Form.useForm();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const {
    page,
    size,
    filter = '{}',
    sort = '',
    id = '',
  } = {
    ...Object.fromEntries(searchParams),
    page: Number(searchParams.get('page') || 1),
    size: Number(searchParams.get('size') || 20),
    id: searchParams.get('id') || '',
  };

  useEffect(() => {
    if (rolesFacade.isEdit && id) {
      rolesFacade.getById({ id });
    }
  }, [id, rolesFacade.isEdit]);

  useEffect(() => {
    switch (rolesFacade.status) {
      case EStatusState.getByIdFulfilled:
        for (const key in rolesFacade.data) {
          roleForm.setFieldValue(key, rolesFacade.data[key as keyof RolesModel]);
        }
        break;
      case EStatusState.putFulfilled:
      case EStatusState.postFulfilled:
        onClose();
        break;
    }
  }, [rolesFacade.status]);

  const onClose = () => {
    if (rolesFacade.isEdit) {
      rolesFacade.set({ isEdit: false });
    }

    if (rolesFacade.isCreate) {
      rolesFacade.set({ isCreate: false });
    }
  };

  const onFinish = (values: any) => {
    if (rolesFacade.isEdit) {
      rolesFacade.put({ ...values, id });
    } else {
      rolesFacade.post({ ...values });
    }
  };

  return (
    <Drawer
      loading={rolesFacade.isLoading}
      title={(rolesFacade.isEdit ? 'Chỉnh sửa' : 'Thêm mới') + ' nhóm người dùng'}
      maskClosable={false}
      onClose={onClose}
      open={rolesFacade.isEdit || rolesFacade.isCreate}
      styles={{
        body: {
          paddingBottom: 80,
        },
      }}
      afterOpenChange={(visible) => {
        if (!visible) {
          roleForm.resetFields();
          setSearchParams(
            (prev) => {
              prev.delete('id');
              return prev;
            },
            { replace: true },
          );
        }
      }}
      footer={
        <Space>
          <Button onClick={onClose}>Huỷ bỏ</Button>
          <Button color="gold" variant="outlined" icon={<UndoOutlined />} onClick={() => roleForm.resetFields()}>
            Nhập lại
          </Button>
          <Button loading={rolesFacade.isLoading} type="primary" htmlType="submit" onClick={() => roleForm.submit()}>
            Lưu lại
          </Button>
        </Space>
      }
    >
      <Form layout="vertical" autoComplete="off" form={roleForm} onFinish={onFinish}>
        <Form.Item
          label={'Mã nhóm người dùng'}
          name={'code'}
          rules={[
            {
              required: true,
              message: 'Mã nhóm người dùng là bắt buộc!',
            },
            {
              pattern: /^[a-zA-Z0-9_]+$/,
              message: 'Mã nhóm người dùng không được chứa ký tự đặc biệt!',
            },
            {
              max: 50,
              message: 'Mã nhóm người dùng không được quá 50 ký tự!',
            },
            // rule viết hoa
            {
              pattern: /^[A-Z0-9_]+$/,
              message: 'Mã nhóm người dùng phải viết hoa!',
            },
          ]}
        >
          <Input placeholder={'Nhập mã nhóm người dùng'} />
        </Form.Item>
        <Form.Item
          label={'Tên nhóm người dùng'}
          name={'name'}
          rules={[{ required: true, message: 'Tên nhóm người dùng là bắt buộc!' }]}
        >
          <Input placeholder={'Nhập tên nhóm người dùng'} />
        </Form.Item>
        <Form.Item label={'Mô Tả'} name={'description'}>
          <TextArea placeholder={'Nhập mô tả'} rows={5} />
        </Form.Item>
      </Form>
    </Drawer>
  );
}
