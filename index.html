<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>ATL Monitor 2025</title>
    <meta name="description" content="ATL Monitor 2025" />
    <meta name="robots" content="noarchive,index,follow" />
    <link id="canonical" rel="canonical" href="http://vanchuyendaichung.geneat.pro" />

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <!-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" /> Chỉ dùng khi có lỗi mixed content và env api đ<PERSON> chuy<PERSON><PERSON> sang https -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover"
    />

    <meta http-equiv="X-UA-Compatible" content="IE=100" />
    <meta name="mobile-web-app-capable" content="yes" />

    <!-- META FOR FACEBOOK -->
    <meta property="fb:app_id" content="110837687935741" />
    <meta property="og:locale" content="vi_VN" />
    <meta property="og:site_name" content="monitordev.geneat.pro/" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/assets/images/logo.png" />

    <meta property="og:image:width" content="596" />
    <meta property="og:image:height" content="596" />
    <meta property="og:url" itemprop="url" content="http://monitordev.geneat.pro/" />
    <meta property="og:title" itemprop="headline" content="ATL Monitor 2025" />
    <!-- END META FOR FACEBOOK -->

    <base href="/" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <meta
      name="google-signin-client_id"
      content="458714151950-fv7bp4u656lk3bel41agq6jh2j4b27pb.apps.googleusercontent.com"
    />
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-L3TLVBTM19"></script>
    <script>
      window.dataLayer = window.dataLayer || [];

      function gtag() {
        dataLayer.push(arguments);
      }

      gtag('js', new Date());

      gtag('config', 'G-L3TLVBTM19');
    </script>
    <link rel="apple-touch-icon" href="assets/icons/logo192.png" />
    <link rel="apple-touch-icon" href="assets/icons/logo512.png" />
    <link rel="manifest" href="manifest.webmanifest" />
    <meta name="theme-color" content="#2563eb" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/modules/pagination/pagination.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/inputmask@5/dist/inputmask.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox@3/dist/css/glightbox.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/echarts@5/dist/echarts.min.js"></script>
    <link
      href="https://cdn.jsdelivr.net/npm/line-awesome@1/dist/line-awesome/css/line-awesome.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/jstat@1/dist/jstat.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@formulajs/formulajs@4/lib/browser/formula.min.js"></script>
    <script>
      Object.keys(formulajs).forEach((key) => {
        window[key] = formulajs[key];
      });
    </script>
  </head>
  <body>
    <div id="root"></div>

    <script type="module" src="/src/index.tsx"></script>
  </body>
</html>
