import { AppBreadCrumb } from '@layouts/admin/AppBreadCrumb';
import { GlobalFacade } from '@store';
import { Button, Dropdown, Flex, Layout, theme } from 'antd';
import { NavBar, TabBar } from 'antd-mobile';
import { AppOutline, LocationOutline } from 'antd-mobile-icons';
import { Content } from 'antd/es/layout/layout';
import React, { ReactNode, useState } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router';
import './index.less';
import { LogoutOutlined, TeamOutlined, UserOutlined } from '@ant-design/icons';
export type T_MasterCtx = {
  tool: [ReactNode, (tool: ReactNode) => void];
  breadCrumb?: ReactNode;
  visible?: boolean;
};

const MasterLayout = () => {
  const [tool, setTool] = useState<ReactNode>(null);
  const { useToken } = theme;
  const globalFacade = GlobalFacade();
  const { user } = globalFacade;
  const [isCollapsed, setIsCollapsed] = useState(innerWidth < 1280);
  const [isDesktop, setIsDesktop] = useState(innerWidth > 1280);

  const navigate = useNavigate();
  const outletCtx: T_MasterCtx = {
    tool: [tool, setTool],
  };
  window.scrollTo({ top: 0, behavior: 'smooth' });

  const menuStyle: React.CSSProperties = {
    boxShadow: 'none',
  };

  const { token } = useToken();
  const location = useLocation();

  const contentStyle: React.CSSProperties = {
    backgroundColor: token.colorBgElevated,
    borderRadius: token.borderRadiusLG,
    boxShadow: token.boxShadowSecondary,
  };

  const tabs = [
    {
      key: '/machines',
      title: 'Danh sách máy',
      icon: <AppOutline />,
    },
        {
      key: '/user-permission',
      title: 'Quản trị',
      icon: <TeamOutlined />,
    },
    {
      key: '/gps',
      title: 'GPS',
      icon: <LocationOutline />,
    },
  ];

  const currentTab = tabs.find((tab) => tab.key === location.pathname);

  return (
    <Layout className="!h-screen flex" style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <Content style={{ flex: 1, overflow: 'auto' }} className={'w-full overflow-auto miniScroll'}>
        <NavBar
          className="bg-white !h-16 fixed top-0 left-0 right-0 z-1"
          backIcon={false}
          right={
            <Dropdown
              trigger={['click']}
              menu={{
                items: [
                  {
                    key: '1',
                    label: (
                      <span className="font-medium">Hi, {(globalFacade.user as any)?.name?.trim()?.split(' ')[0]}</span>
                    ),
                  },
                  {
                    type: 'divider',
                  },
                  {
                    key: '2',
                    label: 'Sign out',
                    extra: <LogoutOutlined />,
                    onClick: () => {
                      globalFacade.logout();
                      navigate('/login');
                    },
                  },
                ],
              }}
            >
              <Button
                type="text"
                size="large"
                color="primary"
                variant="filled"
                icon={<UserOutlined />}
                className="cursor-pointer !rounded-full"
              />
            </Dropdown>
          }
        >
          <div>
            <div className="text-xl font-semibold">{currentTab?.title || 'Danh sách máy'}</div>
          </div>
        </NavBar>
        <Outlet context={outletCtx} />
      </Content>
      <TabBar
        className="bg-white sticky bottom-0 left-0 right-0"
        style={contentStyle}
        safeArea
        onChange={(key) => {
          navigate(key);
        }}
      >
        {tabs.map((item) => (
          <TabBar.Item key={item.key} icon={item.icon} title={item.title} />
        ))}
      </TabBar>
    </Layout>
  );
};

export const SubHeader = (props: {
  children?: React.ReactNode;
  tool?: React.ReactNode;
  breadcrumb?: string;
  isVisible?: boolean;
}) => {
  return (
    <Flex justify="space-between" className="sticky top-0 z-50 !px-3 shadow-header bg-white min-h-12" align="center">
      <AppBreadCrumb breadCrumb={props.breadcrumb ?? ''} />
      <Flex align="center" gap={10}>
        {props.tool}
      </Flex>
    </Flex>
  );
};

export default MasterLayout;
