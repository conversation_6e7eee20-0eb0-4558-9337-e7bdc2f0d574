export const routerLinks = (name: string, type?: string) => {
  const array: {
    [selector: string]: string;
  } = {
    Login: '/auth/login',
    Register: '/auth/register',
    ForgetPassword: '/forgot-password',
    VerifyForotPassword: '/verify-forgot-password',
    SetPassword: '/set-password',
    MyProfile: '/account-information',
    Machines: '/machines',
    PageNotFound: '/*',
    User: '/users',
    RightMapRole: '/authorization',
    Role: '/role',
    Setting: '/setting',
    Data: '/signin-permission',
    CodeType: '/code-type',
    Parameter: '/parameters',
    Navigation: '/navigation',
    DetailMachine: '/machine',
    GPSMachine: '/gps',
  }; // 💬 generate link to here
  const apis: {
    [selector: string]: string;
  } = {
    Auth: '/authentication',
    TypesCodeTypeManagement: '/admin/code-type/types',
    ChartOfAccountCodeTypeManagement: '/admin/code-type/chart-of-accounts',
    CodeTypeManagement: '/admin/code-types',
    CodeType: '/code-type',
    RightMapRole: '/idm/right-map-role',
    Data: '/admin/data',
    DataType: '/admin/datatype',
    Role: '/idm/roles',
    User: '/idm/users',
    Post: '/admin/posts',
    Parameter: '/parameters',
    PostType: '/admin/post-categories',
    Navigation: '/bsd/navigations',
    ObjMapUser: '/obj-map/user',
    Roles: '/idm/roles',
    Machines: '/devices',
  }; // 💬 generate api to here
  switch (type) {
    case 'api':
      return apis[name];
    default:
      return array[name];
  }
};
