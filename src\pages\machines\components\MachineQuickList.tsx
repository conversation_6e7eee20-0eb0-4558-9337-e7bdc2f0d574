import { useEffect, useState } from 'react';
import { Input, Button, Tag, Typography, Space, Spin, Progress, Dropdown, Descriptions } from 'antd';
import {
  LogoutOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined,
  PlusOutlined,
  CaretRightOutlined,
  WarningOutlined,
  CheckCircleFilled,
  CloseCircleFilled,
} from '@ant-design/icons';
import BatteryIcon from '@svgs/battery-40-svgrepo-com.svg?react';
import CrossIcon from '@svgs/cross-svgrepo-com.svg?react';
import MemoryCardIcon from '@svgs/memory-card-svgrepo-com.svg?react';
import TickIcon from '@svgs/tick-svgrepo-com.svg?react';
import TurtleIcon from '@svgs/turtle-svgrepo-com.svg?react';
import { MachinesFacade, MachineModel, GlobalFacade } from '@store';
import { checkAuthAndRedirect } from '@utils';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router';
import { DotLoading, NavBar, PullToRefresh } from 'antd-mobile';
import { useIsMobile } from 'src/utils/hooks/useIsMobile';
import relativeTime from 'dayjs/plugin/relativeTime';

const { Search } = Input;
const { Text, Title } = Typography;

export default function QuickMachines() {
  const machinesFacade = MachinesFacade();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'live' | 'dead' | 'sleep'>('all');
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  useEffect(() => {
    // Check authentication
    if (!checkAuthAndRedirect()) {
      return;
    }

    // Load machines data
    machinesFacade.getMachines({
      time: 300,
      page: 1,
      size: -1,
      filter: '{}',
    });
  }, []);

  const handleRefresh = () => {
    machinesFacade.getMachines({
      time: 300,
      page: 1,
      size: -1,
      filter: '{}',
    });
  };

  const getStatusColor = (status: number, heartbeatStatus: number) => {
    if (status === 2) return '!bg-indigo-100';
    if (status === 1 && heartbeatStatus === 2) return '!bg-lime-200';
    if (status === 1 && heartbeatStatus === 1) return '';
    if (status === 0) return '!bg-slate-300';
    return '!bg-indigo-100';
  };

  const getStatusText = (status: number, heartbeatStatus: number) => {
    if (status === 1 && heartbeatStatus === 1) return 'LIVE';
    if (status === 0) return 'DEAD';
    return 'SLEEP';
  };

  const filteredMachines =
    machinesFacade.pagination?.content?.filter((machine: MachineModel) => {
      const matchesSearch =
        machine.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        machine.deviceId?.toLowerCase().includes(searchTerm.toLowerCase());

      if (statusFilter === 'all') return matchesSearch;

      const machineStatus = getStatusText(machine.status || 0, machine.heartbeatStatus || 0).toLowerCase();
      return matchesSearch && machineStatus === statusFilter;
    }) || [];

  const formatMinsAgo = (minsAgo?: number) => {
    if (minsAgo === undefined || minsAgo === null) return 'N/A';

    // If more than 24 hours (1440 minutes), show date in dd/mm format
    if (minsAgo > 1440) {
      const targetDate = dayjs().subtract(minsAgo, 'minute');
      return targetDate.format('DD/MM');
    }

    if (minsAgo < 60) return `${minsAgo}m`;
    const hours = Math.floor(minsAgo / 60);
    const mins = minsAgo % 60;
    return `${hours}h ${mins}m`;
  };

  const formatLastUpdate = (lastUpdate?: string) => {
    if (!lastUpdate) return 'N/A';
    return dayjs(lastUpdate).format('HH:mm');
  };

  const formatVersionInfo = (machine: MachineModel) => {
    const parts = [];

    if (machine.version) {
      parts.push(machine.version);
    }

    if (machine.backupVersion) {
      parts.push(machine.backupVersion);
    }

    if (machine.networkMode) {
      parts.push(machine.networkMode);
    }

    if (machine.hasMqtt) {
      parts.push('mq');
    }

    return parts.length > 0 ? parts.join('/') : 'N/A';
  };

  const formatMachineName = (name: string) => {
    if (!name) return '';
    const maxLength = 18;
    if (name.length <= maxLength) {
      return name;
    }
    const frontChars = 10;
    const backChars = 4;
    return name.substring(0, frontChars) + '...' + name.substring(name.length - backChars);
  };


  const getMachineDisplayLogic = (machine: MachineModel) => {
    const MAX_UPDATE_MINS = 5;
    const LOW_BATTERY_VOLT = 11.8;
    const MEMORY_USAGE_THRESHOLD = 80;
    const SLOW_CAPTURE_MINS = 120;
    const SLOW_UPLOAD_MINS = 120;
    const SLOW_NETWORK_SPEED = 60; // KB/s

    const isSleep = machine.status === 2;
    const lastUpdatedMinsAgo = machine.lastUpdatedMinsAgo ?? Infinity;
    const isTimedOut = lastUpdatedMinsAgo > MAX_UPDATE_MINS;

    // ABNORMAL
    if (isSleep && machine.wakeTime && dayjs().isAfter(dayjs(machine.wakeTime).add(MAX_UPDATE_MINS, 'minute'))) {
      return { Icon: CrossIcon, iconColor: '#ef4444', textColor: 'text-blue-600' }; // Machine sleep but not waking up
    }
    if (isTimedOut && !isSleep) {
      return { Icon: CrossIcon, iconColor: '#ef4444', textColor: 'text-red-500' }; // Machine OFF / Timed out
    }

    // RISK
    const memoryUsage =
      machine.totalSpace && machine.freeSpace != null
        ? (100 * (machine.totalSpace - machine.freeSpace)) / machine.totalSpace
        : 0;
    if (memoryUsage > MEMORY_USAGE_THRESHOLD) {
      return { Icon: MemoryCardIcon, iconColor: '#f97316', textColor: 'text-amber-900' }; // Memory Full (brown)
    }
    if (machine.batteryVolt && machine.batteryVolt < LOW_BATTERY_VOLT) {
      return { Icon: BatteryIcon, iconColor: '#f97316', textColor: 'text-amber-900' }; // Low Battery (brown)
    }

    // WARNING
    if (
      (machine.uploadSpeed != null && machine.uploadSpeed < SLOW_NETWORK_SPEED) ||
      (machine.lastCapturedImage !== machine.lastUploadedImage && machine.lastCapturedImage) ||
      (machine.lastUploadedMinsAgo != null && machine.lastUploadedMinsAgo > SLOW_UPLOAD_MINS) ||
      (machine.lastCapturedMinsAgo != null && machine.lastCapturedMinsAgo > SLOW_CAPTURE_MINS)
    ) {
      return { Icon: TurtleIcon, iconColor: '#f97316', textColor: 'text-yellow-600' }; // various warnings
    }

    // NORMAL
    if (isSleep) {
      return { Icon: TickIcon, iconColor: '#22c55e', textColor: 'text-blue-600' }; // Machine SLEEP Normal
    }

    return { Icon: TickIcon, iconColor: '#22c55e', textColor: 'text-black' }; // Machine ON Normal
  };

  const machineDetails = (machine: MachineModel) => {
    return (
      <div className="p-4 bg-white shadow-lg rounded-md border w-80">
        <Descriptions
          bordered
          column={1}
          size="small"
          title={<Title level={5}>{machine.deviceId || machine.name}</Title>}
        >
          <Descriptions.Item label="Status">
            <Tag color={getStatusColor(machine.status || 0, machine.heartbeatStatus || 0).replace('!bg-', '')}>
              {getStatusText(machine.status || 0, machine.heartbeatStatus || 0)}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Storage">
            <Progress
              percent={parseFloat((100 - (100 * (machine.freeSpace || 0)) / (machine.totalSpace || 1)).toFixed(2))}
              strokeWidth={8}
              status="active"
              strokeColor={(machine.freeSpace || 0) < 1024 ? '#ff3333' : '#1e90ff'}
            />
          </Descriptions.Item>
          <Descriptions.Item label="Battery">{machine.batteryVolt?.toFixed(2)}V</Descriptions.Item>
          <Descriptions.Item label="Last Update">
            {formatLastUpdate(machine.lastUpdate)} ({formatMinsAgo(machine.lastUpdatedMinsAgo)})
          </Descriptions.Item>
          <Descriptions.Item label="Device Status">
            {machine.status === 1
              ? 'OK'
              : machine.status === 0
                ? 'OFF'
                : 'SLEEP (' + dayjs(machine.wakeTime).format('HH:mm') + ')'}
          </Descriptions.Item>
          <Descriptions.Item label="Last Captured">
            {formatLastUpdate(machine.lastCapturedTime)} ({formatMinsAgo(machine.lastCapturedMinsAgo)})
          </Descriptions.Item>
          <Descriptions.Item label="Last Uploaded">
            {formatLastUpdate(machine.lastUploadedTime)} ({formatMinsAgo(machine.lastUploadedMinsAgo)})
          </Descriptions.Item>
          <Descriptions.Item label="Version">{formatVersionInfo(machine)}</Descriptions.Item>
          <Descriptions.Item label="Upload Speed">{machine.uploadSpeed + 'KB/s'}</Descriptions.Item>
        </Descriptions>
        <Button
          type="primary"
          icon={<CaretRightOutlined />}
          className="mt-4 w-full"
          onClick={() => navigate(`/machine/${machine.deviceId || machine.name || machine.id}/configuration`)}
        >
          View Full Details
        </Button>
      </div>
    );
  };

  if (machinesFacade.isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <>
      <div className="p-4 space-y-4 mt-16 lg:max-w-[1500px] lg:mx-auto">
        <div>
          <div className="flex flex-wrap gap-2 items-center mb-4">
            <Space.Compact>
              <Button type={statusFilter === 'all' ? 'primary' : 'default'} onClick={() => setStatusFilter('all')}>
                All
              </Button>
              <Button type={statusFilter === 'live' ? 'primary' : 'default'} onClick={() => setStatusFilter('live')}>
                Live
              </Button>
              <Button type={statusFilter === 'dead' ? 'primary' : 'default'} onClick={() => setStatusFilter('dead')}>
                Dead
              </Button>
              <Button type={statusFilter === 'sleep' ? 'primary' : 'default'} onClick={() => setStatusFilter('sleep')}>
                Sleep
              </Button>
            </Space.Compact>

            <Search
              placeholder="Nhập để tìm kiếm..."
              allowClear
              style={{ width: '18.75rem', minWidth: '15rem' }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              prefix={<SearchOutlined />}
            />

            {!isMobile && (
              <Button
                type="primary"
                onClick={async () => {
                  handleRefresh();
                }}
              >
                <ReloadOutlined /> Reload
              </Button>
            )}
          </div>
        </div>

        <PullToRefresh
          canReleaseText="Thả để làm mới"
          completeText="Đã làm mới"
          pullingText="Kéo xuống để làm mới"
          refreshingText={
            <>
              <DotLoading /> Đang làm mới...
            </>
          }
          onRefresh={async () => {
            handleRefresh();
          }}
        >
          <div className="rounded-lg shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2">
              {filteredMachines.map((machine: MachineModel, index: number) => {
                const { Icon, iconColor, textColor } = getMachineDisplayLogic(machine);
                return (
                  <div key={machine.id} className="flex items-center p-3 border bg-white rounded-sm border-gray-200">
                    <div className="w-10 h-10 rounded-full mr-4 flex-shrink-0 flex items-center justify-center">
                      <Icon stroke={iconColor} className={`w-7 h-7`} />
                    </div>
                    {/* <div className="flex flex-col"> */}
                      <div className="flex-grow flex items-center overflow-hidden">
                        <span className={`font-bold ${textColor} mr-2`}>{index + 1}.</span>
                        <div title={machine.name || machine.deviceId} className={`font-bold truncate ${textColor}`}>
                          {formatMachineName(machine.name || machine.deviceId || '')}
                        </div>
                      </div>
                      {/* <div>{machine.lastUpdatedMinsAgo != undefined && machine.lastUpdatedMinsAgo > 1440 ? 'Off quá 24h': ( machine.lastUpdatedMinsAgo != undefined && machine.lastUpdatedMinsAgo < 5 ? '' : `Off dưới 24h - ${machine.lastUpdatedMinsAgo}m` )  }</div> */}
                    {/* </div> */}

                    <Dropdown
                      dropdownRender={() => machineDetails(machine)}
                      trigger={['click']}
                      placement="bottomRight"
                    >
                      <Button shape="circle" icon={<PlusOutlined />} />
                    </Dropdown>
                  </div>
                );
              })}
            </div>
          </div>
          {filteredMachines.length === 0 && !machinesFacade.isLoading && (
            <div style={{ textAlign: 'center', marginTop: '3.125rem' }}>
              <Text type="secondary">No machines found</Text>
            </div>
          )}
        </PullToRefresh>
      </div>
    </>
  );
}
