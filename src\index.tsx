import { GlobalFacade, setupStore } from '@store';
import { lang, reportWebVitals } from '@utils';
import { App, ConfigProvider } from 'antd';
import i18n from 'i18next';
import XHR from 'i18next-xhr-backend';
import { lazy, Suspense, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { initReactI18next } from 'react-i18next';
import { Provider } from 'react-redux';
import LoadingScreen from './components/LoadingScreen';
import Router from './router';

import dayjs from 'dayjs';
import 'dayjs/locale/vi'; // Import locale Tiếng Việt cho dayjs
dayjs.locale('vi'); // Đặt ngôn ngữ mặc định cho Dayjs là Tiếng Việt

const fallbackLng = localStorage.getItem('i18nextLng');
if (!fallbackLng) {
  localStorage.setItem('i18nextLng', 'en');
}
i18n
  .use(XHR)
  .use(initReactI18next)
  .init({
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
    fallbackLng: fallbackLng || 'en',
    interpolation: {
      escapeValue: false,
    },
  });
const store = setupStore();
let container: HTMLElement;
const Styling = lazy(() => import('./utils/init/styling'));

const Context = () => {
  const { locale, setLanguage } = GlobalFacade();
  useEffect(() => {
    for (let i = 0; i < localStorage.length; i++) {
      if (localStorage.key(i)?.indexOf('temp-') === 0) {
        localStorage.removeItem(localStorage.key(i) || '');
      }
    }
    setLanguage(lang);
  }, []);

  return (
    <Styling>
      <ConfigProvider
        form={{
          validateMessages: {
            required: '${label} là bắt buộc!',
          },
        }}
        theme={{
          token: {
            controlHeight: 32,
            borderRadius: 3,
          },
          components: {
            Modal: {
              wireframe: true,
            },
          },
        }}
        locale={locale}
      >
        <App>
          <Router />
        </App>
      </ConfigProvider>
    </Styling>
  );
};

document.addEventListener(
  'DOMContentLoaded',
  () => {
    if (!container) {
      container = document.getElementById('root') as HTMLElement;
      const root = createRoot(container);

      root.render(
        <Suspense fallback={<LoadingScreen />}>
          <Provider store={store}>
            <Context />
          </Provider>
        </Suspense>,
      );
    }
  },
  { passive: true },
);

reportWebVitals();
