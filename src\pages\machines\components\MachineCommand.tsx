import { MachinesFacade, DeviceCommandModel, ButtonCommandModel } from '@store';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import {
  Button,
  Card,
  List,
  Space,
  Input,
  Radio,
  Grid,
  DotLoading,
  PullToRefresh,
  Toast,
  Tabs,
  Modal,
  Popover,
} from 'antd-mobile';
import { AddOutline, CloseOutline } from 'antd-mobile-icons';
import { CloseOutlined } from '@ant-design/icons';
import { InputNumber, Tag } from 'antd';

interface Props {
  name?: string;
}

export default function MachineCommand(props: Props) {
  const { name: paramName } = useParams();
  const { name: propName } = props;
  const name = propName || paramName;

  const machinesFacade = MachinesFacade();
  const [modalVisible, setModalVisible] = useState(false);
  const [cmdType, setCmdType] = useState('Pull');
  const [service, setService] = useState('Main');

  const [cmdTypeModal, setCmdTypeModal] = useState('Pull');
  const [serviceModal, setServiceModal] = useState('Main');
  const [newCommandName, setNewCommandName] = useState('');
  const [newCommandValue, setNewCommandValue] = useState('');
  const [activeTab, setActiveTab] = useState('doubleClickType');
  const [popoverVisible, setPopoverVisible] = useState<string | null>(null);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [pendingButton, setPendingButton] = useState<ButtonCommandModel | null>(null);

  const [servoModalVisible, setServoModalVisible] = useState(false);
  const [pendingServoButton, setPendingServoButton] = useState<ButtonCommandModel | null>(null);
  const [servoValue, setServoValue] = useState<number | null>(40);
  const { deviceCommands, buttonCommands, isLoading } = machinesFacade;

  useEffect(() => {
    if (name) {
      machinesFacade.getDeviceCommand(name);
      machinesFacade.getButtonCommand();
    }
  }, [name]);

  const handleAddCommand = async () => {
    if (!newCommandName.trim() || !newCommandValue.trim()) {
      return;
    }

    if (!name) {
      return;
    }

    try {
      await machinesFacade.postDeviceCommand({
        deviceId: name,
        values: {
          name: newCommandName,
          value: newCommandValue,
          type: cmdTypeModal === 'Push' ? 'PUSH_CMD' : 'PULL_CMD',
          service: serviceModal === 'Main' ? 'MAIN' : 'MAINTAIN',
        },
      });

      setNewCommandName('');
      setNewCommandValue('');
      setModalVisible(false);

      // Refresh commands list
      machinesFacade.getDeviceCommand(name);
    } catch (error) {
      console.error('Failed to send command:', error);
    }
  };

  const getButtonColor = (classCommand: string) => {
    switch (classCommand) {
      case 'gn-bg-danger':
        return '#cc6666';
      case 'gn-bg-primary':
        return '#4096ff';
      case 'gn-bg-warning':
        return '#f0c674';
      case 'gn-bg-success':
        return '#b5bd68';
      case 'gn-bg-info':
        return '#8abeb7';
      default:
        return '#f0c674';
    }
  };

  const handleDeleteCommand = async (commandId: string) => {
    if (!name) {
      return;
    }

    try {
      await machinesFacade.deleteDeviceCommand({
        deviceName: name,
        commandId: commandId,
      });

      machinesFacade.getDeviceCommand(name);
    } catch (error) {
      console.error('Failed to delete command:', error);
    }
  };

  const handleButtonClick = async (button: ButtonCommandModel) => {
    if (!name) {
      return;
    }

    // Check if button has dangerous class - show confirmation modal
    if (button.classCommand === 'gn-bg-danger') {
      setPendingButton(button);
      setConfirmModalVisible(true);
      return;
    }

    if (button.valueCommand === 'SETSERVODN' || button.valueCommand === 'SETSERVOUP') {
      setPendingServoButton(button);
      setServoModalVisible(true);
      return;
    }
    // Execute command directly for non-dangerous buttons
    await executeButtonCommand(button);
  };

  const executeButtonCommand = async (button: ButtonCommandModel, servoNumber?: number | null) => {
    if (!name) {
      return;
    }
    try {
      await machinesFacade.postDeviceCommand({
        deviceId: name,
        values: {
          name: button.nameCommand,
          value: button.valueCommand + (servoNumber != null ? ' ' + servoNumber : ''),
          type: cmdType === 'Push' ? 'PUSH_CMD' : 'PULL_CMD',
          service: service === 'Main' ? 'MAIN' : 'MAINTAIN',
        },
      });

      machinesFacade.getDeviceCommand(name);
    } catch (error) {
      console.error('Failed to send button command:', error);
    }
  };

  const handleConfirmDangerousAction = async () => {
    if (pendingButton) {
      await executeButtonCommand(pendingButton);
      setPendingButton(null);
    }
    setConfirmModalVisible(false);
  };

  const handleCancelDangerousAction = () => {
    setPendingButton(null);
    setConfirmModalVisible(false);
  };

  const handleConfirmServoAction = async () => {
    if (pendingServoButton) {
      await executeButtonCommand(pendingServoButton, servoValue);
      setPendingServoButton(null);
    }
    setServoModalVisible(false);
    setServoValue(null);
  };

  const handleCancelServoAction = () => {
    setPendingServoButton(null);
    setServoModalVisible(false);
    setServoValue(null);
  };

  const handleRefresh = async () => {
    if (name) {
      await machinesFacade.getDeviceCommand(name);
    }
  };

  return (
    <div style={{ minHeight: '100vh' }}>
      <Card bodyStyle={{ padding: '0' }} style={{ marginBottom: '1rem' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '0.5rem',
          }}
        >
          <span style={{ fontWeight: 'bold', fontSize: '1rem' }}>Lệnh chờ thực thi (từ trên xuống)</span>
          <Button
            size="small"
            onClick={() => setModalVisible(true)}
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              width: '2rem',
              height: '2rem',
              minWidth: '2rem',
              borderRadius: '50%',
              border: '0px solid #d9d9d9',
            }}
          >
            <AddOutline />
          </Button>
        </div>

        <hr className="border-0 border-t border-gray-200 mb-2 -mx-6" />

        <PullToRefresh
          canReleaseText="Thả để làm mới"
          completeText="Đã làm mới"
          pullingText="Kéo xuống để làm mới"
          refreshingText={
            <>
              <DotLoading /> Đang làm mới...
            </>
          }
          onRefresh={handleRefresh}
        >
          {deviceCommands && deviceCommands.length > 0 ? (
            <List
              style={{
                '--border-bottom': 'none',
                '--border-top': 'none',
                '--font-size': 'clamp(0.75rem, 2.2vw, 1rem)',
              }}
            >
              {deviceCommands.slice().reverse().map((command: DeviceCommandModel) => (
                <List.Item key={command.id}>
                  <div className="flex flex-row justify-between items-center w-full">
                    <div className="flex flex-row gap-1 ">
                      <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>{command.name}</div>
                      <div style={{ fontSize: '0.9rem', color: '#666' }}>{command.value}</div>
                    </div>
                    <div className="flex align-center gap-0.5">
                      <Tag
                        color={command.type === null ? 'default' : command.type === 'PULL_CMD' ? 'success' : 'error'}
                      >
                        {command.type === 'PULL_CMD' ? 'Pull' : 'Push'}
                      </Tag>
                      <Popover
                        content={
                          <div className="flex flex-col gap-2 p-2">
                            <Button
                              size="small"
                              color="danger"
                              fill="solid"
                              onClick={() => {
                                handleDeleteCommand(command.id);
                                setPopoverVisible(null);
                              }}
                              className="text-xs"
                            >
                              Xác nhận
                            </Button>
                            <Button
                              size="small"
                              color="default"
                              fill="outline"
                              onClick={() => setPopoverVisible(null)}
                              className="text-xs"
                            >
                              Huỷ
                            </Button>
                          </div>
                        }
                        visible={popoverVisible === command.id}
                        onVisibleChange={(visible) => {
                          if (visible) {
                            setPopoverVisible(command.id);
                          } else {
                            setPopoverVisible(null);
                          }
                        }}
                        trigger="click"
                        placement="left"
                      >
                        <Button
                          size="small"
                          className="rounded !bg-amber-500 !text-white border-0 px-2 py-1 !text-xs"
                        >
                          Xoá
                        </Button>
                      </Popover>
                    </div>
                  </div>
                </List.Item>
              ))}
            </List>
          ) : (
            <div style={{ textAlign: 'center', color: '#999', marginTop: '0.5rem' }}>Chưa có lệnh nào</div>
          )}
        </PullToRefresh>
      </Card>
      <hr className="border-0 border-t border-gray-200 mb-2 -mx-3" />
      {/* CMD Type and Service Selection */}
      <Card bodyStyle={{ padding: '0' }} className="mb-4 p-4 mt-4">
        <div className="grid grid-cols-3 gap-2">
          <div className=" flex-none font-medium text-green-500">CMD Type</div>
          <Radio.Group value={cmdType} onChange={(val) => setCmdType(val as string)}>
            <Radio value="Push" >
              <div className="text-sm">Push</div>
            </Radio>
            <Radio value="Pull" className="text-sm">
              <div className="text-sm">Pull</div>
            </Radio>
          </Radio.Group>
          <div className=" flex-none font-medium text-green-500 ">Service</div>
          <Radio.Group value={service} onChange={(val) => setService(val as string)}>
            <Radio value="Maintain" className="text-sm">
              <div className="text-sm">Maintain</div>
            </Radio>
            <Radio value="Main" className="text-sm">
              <div className="text-sm">Main</div>
            </Radio>
          </Radio.Group>
        </div>
      </Card>

      {/* Command Tabs */}
      {buttonCommands && (
        <Tabs style={{ '--title-font-size': '14px' }} activeKey={activeTab} onChange={setActiveTab}>
          <Tabs.Tab title="Điều khiển" key="doubleClickType">
            <Grid columns={2} gap={8}>
              {buttonCommands.doubleClickType?.map((button, index) => (
                <Grid.Item key={index}>
                  <Button
                    size="small"
                    fill="solid"
                    onClick={() => handleButtonClick(button)}
                    style={{
                      width: '100%',
                      minHeight: '3rem',
                      fontSize: '0.8rem',
                      backgroundColor: getButtonColor(button.classCommand),
                      color: button.classCommand === 'gn-bg-warning' ? '#333' : 'white',
                      border: 'none',
                    }}
                  >
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontWeight: 'bold' }}>{button.buttonLabelCommand}</div>
                      <div style={{ fontSize: '0.7rem' }}>{button.labelCommand}</div>
                    </div>
                  </Button>
                </Grid.Item>
              ))}
            </Grid>
          </Tabs.Tab>

          <Tabs.Tab title="Update" key="updateType">
            <Grid columns={2} gap={8}>
              {buttonCommands.updateType?.map((button, index) => (
                <Grid.Item key={index}>
                  <Button
                    size="small"
                    fill="solid"
                    onClick={() => handleButtonClick(button)}
                    style={{
                      width: '100%',
                      minHeight: '3rem',
                      fontSize: '0.8rem',
                      backgroundColor: getButtonColor('gn-bg-primary'),
                      color: button.classCommand === 'gn-bg-warning' ? '#333' : 'white',
                      border: 'none',
                    }}
                  >
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontWeight: 'bold' }}>{button.buttonLabelCommand}</div>
                      <div style={{ fontSize: '0.7rem' }}>{button.labelCommand}</div>
                    </div>
                  </Button>
                </Grid.Item>
              ))}
            </Grid>
          </Tabs.Tab>

          <Tabs.Tab title="Log" key="logType">
            <Grid columns={2} gap={8}>
              {buttonCommands.logType?.map((button, index) => (
                <Grid.Item key={index}>
                  <Button
                    size="small"
                    fill="solid"
                    onClick={() => handleButtonClick(button)}
                    style={{
                      width: '100%',
                      minHeight: '3rem',
                      fontSize: '0.8rem',
                      backgroundColor: getButtonColor(button.classCommand),
                      color: button.classCommand === 'gn-bg-warning' ? '#333' : 'white',
                      border: 'none',
                    }}
                  >
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontWeight: 'bold' }}>{button.buttonLabelCommand}</div>
                      <div style={{ fontSize: '0.7rem' }}>{button.labelCommand}</div>
                    </div>
                  </Button>
                </Grid.Item>
              ))}
            </Grid>
          </Tabs.Tab>

          <Tabs.Tab title="Analog" key="analogType">
            <Grid columns={2} gap={8}>
              {buttonCommands.analogType?.map((button, index) => (
                <Grid.Item key={index}>
                  <Button
                    size="small"
                    fill="solid"
                    onClick={() => handleButtonClick(button)}
                    style={{
                      width: '100%',
                      minHeight: '3rem',
                      fontSize: '0.8rem',
                      backgroundColor: getButtonColor(button.classCommand),
                      color: button.classCommand === 'gn-bg-warning' ? '#333' : 'white',
                      border: 'none',
                    }}
                  >
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontWeight: 'bold' }}>{button.buttonLabelCommand}</div>
                      <div style={{ fontSize: '0.7rem' }}>{button.labelCommand}</div>
                    </div>
                  </Button>
                </Grid.Item>
              ))}
            </Grid>
          </Tabs.Tab>
        </Tabs>
      )}

      <Modal
        bodyStyle={{ padding: '0' }}
        className="!rounded-xs"
        visible={modalVisible}
        content={
          <div className="py-1 px-3  bg-white">
            {/* Header */}
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Thêm lệnh</h2>
              <Button
                fill="none"
                onClick={() => setModalVisible(false)}
                className="text-gray-400 !text-lg hover:text-gray-600 "
              >
                <CloseOutlined />
              </Button>
            </div>
            <hr className="border-0 border-t border-gray-200 mb-5 -mx-6" />
            {/* Inputs */}
            <div className="space-y-4 mb-6">
              <Input
                style={{ '--font-size': 'clamp(0.75rem, 2.2vw, 1rem)' }}
                placeholder="Nhập lệnh"
                value={newCommandName}
                onChange={setNewCommandName}
                className="w-full border border-gray-300 rounded-xs px-3 py-2"
              />

              <Input
                style={{ '--font-size': 'clamp(0.75rem, 2.2vw, 1rem)' }}
                placeholder="Giá trị lệnh"
                value={newCommandValue}
                onChange={setNewCommandValue}
                className="w-full border border-gray-300 rounded-xs px-3 py-2"
              />
            </div>

            <div className="mb-6">
              <div
                style={{ fontSize: 'clamp(0.75rem, 2.2vw, 1rem)' }}
                className="text-sm font-medium text-teal-600 mb-3"
              >
                CMD Type
              </div>
              <Radio.Group value={cmdTypeModal} onChange={(val) => setCmdTypeModal(val as string)}>
                <div className="flex gap-6">
                  <Radio value="Push" className="text-gray-700">
                    <div style={{ fontSize: 'clamp(0.75rem, 2.2vw, 1rem)' }}>Push</div>
                  </Radio>
                  <Radio value="Pull" className="text-gray-700">
                    <div style={{ fontSize: 'clamp(0.75rem, 2.2vw, 1rem)' }}>Pull</div>
                  </Radio>
                </div>
              </Radio.Group>
            </div>

            <div className="mb-8">
              <div
                style={{ fontSize: 'clamp(0.75rem, 2.2vw, 1rem)' }}
                className="text-sm font-medium text-teal-600 mb-3"
              >
                Service
              </div>
              <Radio.Group value={serviceModal} onChange={(val) => setServiceModal(val as string)}>
                <div style={{ fontSize: 'clamp(0.75rem, 2.2vw, 1rem)' }} className="flex gap-6">
                  <Radio value="Maintain" className="text-gray-700">
                    <div style={{ fontSize: 'clamp(0.75rem, 2.2vw, 1rem)' }}>Maintain</div>
                  </Radio>
                  <Radio value="Main" className="text-gray-700">
                    <div style={{ fontSize: 'clamp(0.75rem, 2.2vw, 1rem)' }}>Main</div>
                  </Radio>
                </div>
              </Radio.Group>
            </div>
            <hr className="border-0 border-t border-gray-200 mb-2 -mx-6" />
            {/* Add Button */}
            <div className="flex justify-end">
              <Button
                color="primary"
                fill="solid"
                onClick={handleAddCommand}
                loading={isLoading}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md font-medium"
              >
                <div style={{ fontSize: 'clamp(0.75rem, 2.2vw, 1rem)' }}>Add</div>
              </Button>
            </div>
          </div>
        }
        closeOnAction={false}
        onClose={() => setModalVisible(false)}
      />

      <Modal
        visible={confirmModalVisible}
        content={
          <div className="py-4 px-6 bg-white">
            <div className="text-center">
              <div className="mb-4">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                  <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <circle cx="12" cy="17" r="1" fill="#000000" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10L12 14" />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3.44722 18.1056L10.2111 4.57771C10.9482 3.10361 13.0518 3.10362 13.7889 4.57771L20.5528 18.1056C21.2177 19.4354 20.2507 21 18.7639 21H5.23607C3.7493 21 2.78231 19.4354 3.44722 18.1056Z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Xác nhận hành động</h3>
                <p className="text-sm text-gray-500 mb-6">
                  Bạn có chắc chắn muốn thực hiện lệnh "{pendingButton?.buttonLabelCommand}" không? Hành động này có thể
                  ảnh hưởng đến hoạt động của thiết bị.
                </p>
              </div>

              <div className="flex gap-3 justify-center">
                <Button color="default" fill="outline" onClick={handleCancelDangerousAction} className="px-6 py-2">
                  Hủy
                </Button>
                <Button color="danger" fill="solid" onClick={handleConfirmDangerousAction} className="px-6 py-2">
                  Xác nhận
                </Button>
              </div>
            </div>
          </div>
        }
        closeOnAction={false}
        onClose={handleCancelDangerousAction}
      />

      <Modal
        visible={servoModalVisible}
        content={
          <div className="py-4 px-6 bg-white">
            <div className="text-center">
              <div className="mb-4">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                  <svg className="h-6 w-6 text-blue-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      d="M12 7V13M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <circle cx="12" cy="16.5" r="1" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Xác nhận hành động</h3>
                <p className="text-sm text-gray-500 mb-6">
                  Thực hiện lệnh "{pendingServoButton?.buttonLabelCommand}" với góc quay:
                </p>
                <InputNumber
                  addonBefore={
                    <div
                      onClick={() => {
                        setServoValue(servoValue != null ? servoValue + 1 : 1);
                      }}
                    >
                      +
                    </div>
                  }
                  value={servoValue}
                  onChange={(value) => {
                    setServoValue(value ?? 0);
                  }}
                  controls={false}
                  addonAfter={
                    <div
                      onClick={() => {
                        setServoValue(servoValue != null ? servoValue - 1 : 0);
                      }}
                    >
                      -
                    </div>
                  }
                />
              </div>

              <div className="flex gap-3 justify-center">
                <Button color="default" fill="outline" onClick={handleCancelServoAction} className="px-6 py-2">
                  Hủy
                </Button>
                <Button color="primary" fill="solid" onClick={handleConfirmServoAction} className="px-6 py-2">
                  Xác nhận
                </Button>
              </div>
            </div>
          </div>
        }
        closeOnAction={false}
        onClose={handleCancelDangerousAction}
      />
    </div>
  );
}
