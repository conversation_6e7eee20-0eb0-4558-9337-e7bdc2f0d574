/**
 * Chart Monitor Component
 *
 * React TypeScript implementation of the Angular chart monitoring component.
 * Displays battery, upload speed, temperature, and humidity charts with data tables in tabs.
 */

import { useEffect, useRef, useState } from 'react';
import { Tabs, Button } from 'antd-mobile';
import { useParams } from 'react-router';
import * as echarts from 'echarts';
import { MachinesFacade } from '@store';
import { ChartType, DataTableColumn } from '../../utils/types/chart.types';
import { getChartConfig, generateChartOption, formatDate, validateChartData } from '../../utils/chartUtils';
import DataTable from './components/DataTable';

/**
 * Chart Monitor Component
 */
export default function MachineCharts() {
  const { name } = useParams();
  const machinesFacade = MachinesFacade();
  const [activeTab, setActiveTab] = useState<ChartType>('battery');
  const chartRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const echartsInstances = useRef<Record<string, echarts.ECharts>>({});
  const [batteryCount, setBatteryCount] = useState(20);
  const [uploadSpeedCount, setUploadSpeedCount] = useState(20);
  const [temperatureCount, setTemperatureCount] = useState(20);
  const [humidityCount, setHumidityCount] = useState(20);

  // Chart configurations
  const chartConfigs = [
    getChartConfig('battery'),
    getChartConfig('uploadSpeed'),
    getChartConfig('temperature'),
    getChartConfig('humidity'),
  ];

  // Debug chart configs
  console.log('Chart configs:', chartConfigs);

  /**
   * Initialize ECharts instance
   */
  const initChart = (elementId: string, type: ChartType) => {
    console.log(`Initializing chart for ${type} with elementId: ${elementId}`);
    const element = chartRefs.current[elementId];
    if (!element) {
      console.log(`Element not found for ${elementId}`);
      return;
    }

    // Dispose existing instance
    if (echartsInstances.current[elementId]) {
      echartsInstances.current[elementId].dispose();
    }

    // Create new instance
    const chart = echarts.init(element);
    echartsInstances.current[elementId] = chart;
    console.log(`Chart initialized for ${type}`);

    // Handle resize
    const handleResize = () => chart.resize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.dispose();
    };
  };

  /**
   * Update chart with new data
   */
  const updateChart = (elementId: string, type: ChartType) => {
    console.log(`Updating chart for ${type} with elementId: ${elementId}`);
    const chart = echartsInstances.current[elementId];
    const chartData = getCurrentChartData(type);

    console.log(`Chart instance exists: ${!!chart}, Data length: ${chartData?.length || 0}`);

    if (!chart) {
      console.log(`No chart instance found for ${elementId}`);
      return;
    }

    if (!chartData || chartData.length === 0) {
      console.log(`No data available for ${type}`);
      return;
    }

    const config = getChartConfig(type);
    const validatedData = validateChartData(chartData);
    const option = generateChartOption(validatedData, config);

    console.log(`Setting chart option for ${type}:`, option);
    chart.setOption(option as any, true);
  };

  /**
   * Handle size increase for specific chart
   */
  const handleSizeIncrease = (type: ChartType, increment: number) => {
    if (!name) return;

    console.log(`Increasing ${type} count by ${increment}`);

    switch (type) {
      case 'battery':
        const newBatteryCount = batteryCount + increment;
        setBatteryCount(newBatteryCount);
        machinesFacade.getBatteryChart({ deviceId: name, count: newBatteryCount });
        break;
      case 'uploadSpeed':
        const newUploadSpeedCount = uploadSpeedCount + increment;
        setUploadSpeedCount(newUploadSpeedCount);
        machinesFacade.getUploadSpeedChart({ deviceId: name, count: newUploadSpeedCount });
        break;
      case 'temperature':
        const newTemperatureCount = temperatureCount + increment;
        setTemperatureCount(newTemperatureCount);
        machinesFacade.getTemperatureChart({ deviceId: name, count: newTemperatureCount });
        break;
      case 'humidity':
        const newHumidityCount = humidityCount + increment;
        setHumidityCount(newHumidityCount);
        machinesFacade.getHumidityChart({ deviceId: name, count: newHumidityCount });
        break;
    }
  };

  /**
   * Get current chart data based on type
   */
  const getCurrentChartData = (type: ChartType) => {
    switch (type) {
      case 'battery':
        return machinesFacade.batteryChart;
      case 'uploadSpeed':
        return machinesFacade.uploadSpeedChart;
      case 'temperature':
        return machinesFacade.temperatureChart;
      case 'humidity':
        return machinesFacade.humidityChart;
      default:
        return null;
    }
  };

  /**
   * Generate data table columns for each chart type
   */
  const getTableColumns = (type: ChartType): DataTableColumn[] => {
    const config = getChartConfig(type);

    return [
      {
        key: 'value',
        title:
          type === 'battery'
            ? 'Điện áp'
            : type === 'uploadSpeed'
              ? 'Tốc độ Upload'
              : type === 'temperature'
                ? 'Nhiệt độ'
                : 'Độ ẩm',
        width: '120px',
        render: (value: number) => `${value}${config.unit}`,
      },
      {
        key: 'createdOnDate',
        title: 'Giờ ghi nhận',
        width: '150px',
        render: (value: string | Date) => formatDate(value),
      },
    ];
  };

  // Initialize chart for active tab
  useEffect(() => {
    const activeConfig = chartConfigs.find((config) => config.type === activeTab);
    if (activeConfig) {
      console.log(`Active tab changed to: ${activeTab}, config:`, activeConfig);
      // Small delay to ensure DOM element is rendered
      setTimeout(() => {
        initChart(activeConfig.elementId, activeConfig.type);
        // Update chart immediately after initialization if data is available
        const chartData = getCurrentChartData(activeConfig.type);
        if (chartData && chartData.length > 0) {
          setTimeout(() => {
            updateChart(activeConfig.elementId, activeConfig.type);
          }, 50);
        }
      }, 100);
    }

    return () => {
      // Only dispose the current chart instance
      const activeConfig = chartConfigs.find((config) => config.type === activeTab);
      if (activeConfig && echartsInstances.current[activeConfig.elementId]) {
        echartsInstances.current[activeConfig.elementId].dispose();
        delete echartsInstances.current[activeConfig.elementId];
      }
    };
  }, [activeTab]);

  // Update active chart when data changes
  useEffect(() => {
    console.log('Chart data updated:', {
      battery: machinesFacade.batteryChart?.length || 0,
      uploadSpeed: machinesFacade.uploadSpeedChart?.length || 0,
      temperature: machinesFacade.temperatureChart?.length || 0,
      humidity: machinesFacade.humidityChart?.length || 0,
    });

    const activeConfig = chartConfigs.find((config) => config.type === activeTab);
    if (activeConfig) {
      const chartData = getCurrentChartData(activeConfig.type);
      if (chartData && chartData.length > 0) {
        console.log(`Updating chart for ${activeConfig.type} with ${chartData.length} data points`);
        // Small delay to ensure chart is initialized
        setTimeout(() => {
          updateChart(activeConfig.elementId, activeConfig.type);
        }, 150);
      }
    }
  }, [
    machinesFacade.batteryChart,
    machinesFacade.uploadSpeedChart,
    machinesFacade.temperatureChart,
    machinesFacade.humidityChart,
  ]);

  // Load initial data for each chart type separately
  useEffect(() => {
    if (name) {
      machinesFacade.getBatteryChart({ deviceId: name, count: batteryCount });
    }
  }, [name, batteryCount]);

  useEffect(() => {
    if (name) {
      machinesFacade.getTemperatureChart({ deviceId: name, count: temperatureCount });
    }
  }, [name, temperatureCount]);

  useEffect(() => {
    if (name) {
      machinesFacade.getHumidityChart({ deviceId: name, count: humidityCount });
    }
  }, [name, humidityCount]);

  useEffect(() => {
    if (name) {
      machinesFacade.getUploadSpeedChart({ deviceId: name, count: uploadSpeedCount });
    }
  }, [name, uploadSpeedCount]);

  const currentData = getCurrentChartData(activeTab);

  return (
    <div className="bg-white min-h-screen text-[14px] max-[450px]:!text-[13px]">
      <Tabs className="bg-white" activeKey={activeTab} onChange={(key) => setActiveTab(key as ChartType)}>
        {chartConfigs.map((chartConfig) => (
          <Tabs.Tab
            title={
              chartConfig.title.replace('Biểu đồ ', '').charAt(0).toUpperCase() +
              chartConfig.title.replace('Biểu đồ ', '').slice(1)
            }
            key={chartConfig.type}
          >
            <div className="p-2 pt-1">
              {/* Chart Section */}
              <div className="mb-6">
                <div className="flex items-center justify-evenly mb-4 mx-[2rem]">
                  <Button fill="outline" color="primary" onClick={() => handleSizeIncrease(chartConfig.type, 20)}>
                    <div className="font-semibold text-[14px] max-[450px]:!text-[13px]">+ 20 mẫu</div>
                  </Button>
                  <Button fill="outline" color="primary" onClick={() => handleSizeIncrease(chartConfig.type, 100)}>
                    <div className="font-semibold text-[14px] max-[450px]:!text-[13px]">+ 100 mẫu</div>
                  </Button>
                </div>

                {/* Chart Container */}
                <div className="relative mb-4 border-1 rounded-lg p-2 border-gray-200">
                  <h3 className="text-lg max-[450px]:!text-[13px] font-semibold text-gray-900">{chartConfig.title}</h3>
                  <div
                    ref={(el) => (chartRefs.current[chartConfig.elementId] = el)}
                    className="w-full h-80 rounded-lg"
                    style={{ height: '300px' }}
                  />
                </div>

                {/* DataTable Container */}
                <div className="relative">
                  <DataTable
                    columns={getTableColumns(chartConfig.type)}
                    data={currentData || []}
                    loading={false}
                    rowHeight="40px"
                    className="h-80 overflow-y-scroll text-[14px] max-[450px]:!text-[13px]"
                  />
                </div>
              </div>
            </div>
          </Tabs.Tab>
        ))}
      </Tabs>
    </div>
  );
}
