/**
 * Chart Utilities
 * 
 * This file contains utility functions for chart operations,
 * including data formatting, color conversion, and chart configuration.
 */

import { ChartDataPoint, ChartConfig, EChartsOption } from './types/chart.types';

/**
 * Chart color palette
 */
export const CHART_COLORS = [
  '#0090FF', // Blue
  '#36CE9E', // Green
  '#FFC005', // Yellow
  '#FF515A', // Red
  '#8B5CFF', // Purple
  '#00CA69', // Emerald
];

/**
 * Format date for display
 */
export const formatDate = (date: string | Date): string => {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return 'Invalid Date';
  }

  // Format as DD/MM HH:mm
  const day = d.getDate().toString().padStart(2, '0');
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const hours = d.getHours().toString().padStart(2, '0');
  const minutes = d.getMinutes().toString().padStart(2, '0');
  
  return `${day}/${month} ${hours}:${minutes}`;
};

/**
 * Format chart data for ECharts
 */
export const formatChartData = (data: ChartDataPoint[]): {
  xAxisData: string[];
  yAxisData: number[];
} => {
  const xAxisData = data.map(item => formatDate(item.createdOnDate));
  const yAxisData = data.map(item => item.value);
  
  return { xAxisData, yAxisData };
};

/**
 * Convert hex color to RGBA
 */
export const hexToRgba = (hex: string, opacity: number): string => {
  const reg = /^#[\da-f]{6}$/i;
  
  if (!reg.test(hex)) {
    return `rgba(0, 0, 0, ${opacity})`;
  }
  
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

/**
 * Generate ECharts option configuration
 */
export const generateChartOption = (
  data: ChartDataPoint[],
  config: ChartConfig
): EChartsOption => {
  const { xAxisData, yAxisData } = formatChartData(data);
  const color = CHART_COLORS[config.colorIndex] || CHART_COLORS[0];

  return {
    color: CHART_COLORS,
    legend: { show: false },
    grid: {
      left: '1%',
      top: '20px',
      right: '50px',
      bottom: '1%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        if (Array.isArray(params) && params.length > 0) {
          const param = params[0];
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">
                ${param.name}
              </div>
              <div style="display: flex; align-items: center;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
                <span>${param.value}${config.unit}</span>
              </div>
            </div>
          `;
        }
        return '';
      },
      extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 6px;',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    xAxis: {
      type: 'category',
      name: 'Giờ',
      boundaryGap: false,
      axisLabel: {
        textStyle: {
          color: '#333',
        },
      },
      nameTextStyle: {
        color: '#666',
        fontSize: 12,
        lineHeight: 1,
        fontWeight: 'bold',
      },
      axisLine: {
        lineStyle: {
          color: '#D9D9D9',
        },
      },
      data: xAxisData,
    },
    yAxis: {
      type: 'value',
      name: config.unit,
      scale: true,
      axisLabel: {
        textStyle: {
          color: '#666',
        },
      },
      nameTextStyle: {
        color: '#666',
        fontSize: 12,
        lineHeight: 1,
        fontWeight: 'bold',
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E9E9E9',
        },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    series: [
      {
        name: config.title,
        type: 'line',
        smooth: true,
        symbolSize: 8,
        zlevel: 3,
        itemStyle: {
          color: color,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: hexToRgba(color, 0.3)
              },
              {
                offset: 1,
                color: hexToRgba(color, 0.1)
              }
            ]
          }
        },
        data: yAxisData,
      },
    ],
  };
};

/**
 * Get chart configuration by type
 */
export const getChartConfig = (type: string): ChartConfig => {
  const configs: Record<string, ChartConfig> = {
    battery: {
      type: 'battery',
      title: 'Biểu đồ điện áp pin',
      unit: 'V',
      colorIndex: 0,
      elementId: 'chart-time-series',
      apiEndpoint: 'battery'
    },
    uploadSpeed: {
      type: 'uploadSpeed',
      title: 'Biểu đồ tốc độ upload',
      unit: 'KB/s',
      colorIndex: 1,
      elementId: 'chart-upload-speed',
      apiEndpoint: 'upload_speed'
    },
    temperature: {
      type: 'temperature',
      title: 'Biểu đồ nhiệt độ',
      unit: '°C',
      colorIndex: 2,
      elementId: 'chart-temperature',
      apiEndpoint: 'temperature'
    },
    humidity: {
      type: 'humidity',
      title: 'Biểu đồ độ ẩm',
      unit: '%',
      colorIndex: 3,
      elementId: 'chart-humidity',
      apiEndpoint: 'humidity'
    }
  };

  return configs[type] || configs.battery;
};

/**
 * Validate chart data
 */
export const validateChartData = (data: any[]): ChartDataPoint[] => {
  if (!Array.isArray(data)) {
    return [];
  }

  return data.filter(item => 
    item && 
    typeof item.value === 'number' && 
    !isNaN(item.value) &&
    item.createdOnDate
  ).map(item => ({
    value: item.value,
    createdOnDate: item.createdOnDate,
    metadata: item.metadata || {}
  }));
};

/**
 * Calculate chart statistics
 */
export const calculateChartStats = (data: ChartDataPoint[]) => {
  if (data.length === 0) {
    return {
      min: 0,
      max: 0,
      avg: 0,
      latest: 0,
      count: 0
    };
  }

  const values = data.map(item => item.value);
  const min = Math.min(...values);
  const max = Math.max(...values);
  const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
  const latest = values[values.length - 1];

  return {
    min: Math.round(min * 100) / 100,
    max: Math.round(max * 100) / 100,
    avg: Math.round(avg * 100) / 100,
    latest: Math.round(latest * 100) / 100,
    count: data.length
  };
};

/**
 * Export all utilities as an object
 */
export const chartUtils = {
  formatDate,
  formatChartData,
  hexToRgba,
  generateChartOption,
  getChartConfig,
  validateChartData,
  calculateChartStats,
  CHART_COLORS
};

export default chartUtils;
