import { GlobalFacade } from '@store';
import { Drawer, Image } from 'antd';
import Sider from 'antd/es/layout/Sider';
import { NavLink } from 'react-router-dom';
import AntMenu from '../ant-menu';

export default function DrawerMenu() {
  const globalFacade = GlobalFacade();
  const onClose = () => {
    globalFacade.set({ isShowDrawerSider: false });
  };
  return (
    <Drawer
      width={250}
      styles={{ body: { padding: 0 } }}
      placement="left"
      title={false}
      footer={false}
      open={globalFacade.isShowDrawerSider}
      closable={false}
      onClose={onClose}
    >
      <Sider width={250} theme="light">
        <NavLink id="ant-sider-logo" to={'/vn/dashboard'}>
          <Image preview={false} src={'/assets/images/logo.png'} fallback="/assets/images/no-image.png" alt="logo" />
        </NavLink>
        <AntMenu />
      </Sider>
    </Drawer>
  );
}
