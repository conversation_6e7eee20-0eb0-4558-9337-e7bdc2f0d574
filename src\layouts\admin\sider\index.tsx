import { Image } from 'antd';
import Sider from 'antd/es/layout/Sider';
import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import AntMenu from '../ant-menu';
import DrawerMenu from '../drawer-sider';

interface AntSiderProps {
  isCollapsed: boolean;
  setIsCollapsed: (value: boolean) => void;
}
export default function AntSider(props: AntSiderProps) {
  const [collapsedWidth, setCollapsedWidth] = useState(60);

  const onBreakpoint = (broken: boolean) => {
    if (broken) {
      // Màn nhỏ hơn lg (breakpoint bị kích hoạt)
      setCollapsedWidth(0);
    } else {
      // Màn >= lg
      setCollapsedWidth(60);
    }
  };
  return (
    <>
      <Sider
        breakpoint="lg"
        collapsedWidth={collapsedWidth}
        width="15.625rem"
        theme="light"
        trigger={null}
        collapsible
        collapsed={props.isCollapsed}
        onCollapse={(value) => props.setIsCollapsed(value)}
        onBreakpoint={onBreakpoint}
      >
        <NavLink id="ant-sider-logo" to={'/vn/dashboard'}>
          <Image preview={false} src={'/assets/images/logo.png'} fallback="/assets/images/no-image.png" alt="logo" />
        </NavLink>
        <AntMenu />
      </Sider>
      <DrawerMenu />
    </>
  );
}
