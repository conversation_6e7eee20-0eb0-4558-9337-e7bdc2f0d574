import { Attachment, EStatusMachine, MachinesFacade } from '@store';
import { FC, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router';
import { Button, Input, Upload, Modal, TabsProps, Tabs, Divider, App } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import { formatDayjsDate } from '@utils';
import { CloseOutlined, SaveOutlined, UploadOutlined } from '@ant-design/icons';

const DeviceUploadPage: FC = () => {
  const { modal } = App.useApp();
  const { name } = useParams();
  const machinesFacade = MachinesFacade();
  const tmpFiles = useRef<Record<string, UploadFile>>({});
  const [previewVisible, setPreviewVisible] = useState(false);
  const [uploadForDoctype, setUploadForDoctype] = useState<string | null>(null);
  const [description, setDescription] = useState<string>('');
  const [preview, setPreview] = useState<any>(null);
  const [pendingFile, setPendingFile] = useState<UploadFile | null>(null);
  // const listUpload = useMemo(() => {
  //   return Object.keys(machinesFacade.listUpload ?? {}).reduce(
  //     (prev, docType) => {
  //       return {
  //         ...prev,
  //         [docType]: machinesFacade.listUpload?.[docType]?.map((item) => tmpFiles.current[item.uid] || item) ?? [],
  //       };
  //     },
  //     {} as Record<string, UploadFile[]>,
  //   );
  // }, [machinesFacade.listUpload]);

  const handleUpload = () => {
    if (!pendingFile || pendingFile.status !== 'done') return;

    machinesFacade.createAttachmentsTemplate(name as string, {
      ...machinesFacade.infoUpload?.find((item) => item.docType === uploadForDoctype),
      file: pendingFile.response.data.physicalPath,
      description: description,
    });
    setUploadForDoctype(null);
    setPendingFile(null);
    setDescription('');
  };

  // const handlePreview = (file: UploadFile) => {
  //   setPreviewVisible(true);
  //   setPreview(file);
  // };

  const handleRemove = (file: Attachment) => {
    modal.confirm({
      title: 'Xoá ảnh',
      content: 'Bạn có chắc chắn muốn xoá ảnh này không?',
      okText: 'Xoá',
      cancelText: 'Hủy',
      centered: true,
      onOk: () => {
        if (name) {
          machinesFacade.deleteImage(name, file.id);
        }
      },
    });
  };

  const tabItems: TabsProps['items'] = useMemo(() => {
    return machinesFacade.infoUpload?.map((item) => ({
      key: item.docType,
      label: item.docTypeName,
      children: (
        <div className="-mx-2">
          <div className="mx-auto w-fit pb-4">
            <Button icon={<UploadOutlined />} type="primary" onClick={() => setUploadForDoctype(item.docType)}>
              Tải ảnh lên
            </Button>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6">
            {machinesFacade.listUpload?.[item.docType]?.map((attachment) => (
              <div key={attachment.id} className="h-full relative">
                <Divider className="!my-0 absolute top-0 left-0 w-full" />
                <Button
                  icon={<CloseOutlined />}
                  className="!absolute top-1 right-2"
                  type="text"
                  danger
                  size="small"
                  onClick={() => handleRemove(attachment)}
                />
                <div className="h-full p-4 pt-8 flex flex-col items-center">
                  <div className="flex-1 flex items-center">
                    <img src={attachment.file} alt={attachment.file?.split('/').pop()} className="w-full" />
                  </div>
                  <div className="w-full mt-2 space-y-2">
                    <p className="text-wrap text-center">Mô tả: {attachment.description}</p>
                    <p className="text-center">{formatDayjsDate(attachment.createdOnDate, 'HH:mm:ss DD/MM/YYYY')}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ),
    }));
  }, [machinesFacade.listUpload]);

  useEffect(() => {
    if (name) {
      machinesFacade.getByIdToAttachmentsTemplate(name);
    }

    return () => {
      machinesFacade.set({
        infoUpload: [],
        listUpload: {},
        showList: {},
        description: {},
      });
    };
  }, [name]);

  useEffect(() => {
    switch (machinesFacade.status) {
      case EStatusMachine.getByIdToAttachmentsTemplateFulfilled:
      case EStatusMachine.createAttachmentsTemplateFulfilled:
      case EStatusMachine.deleteImageFulfilled:
        machinesFacade.infoUpload?.forEach((item) => {
          machinesFacade.getAttachments({ name: name as string, docType: item.docType });
        });
        break;
    }
  }, [machinesFacade.status]);

  return (
    <>
      <div className="container mx-auto space-y-2 p-2 bg-white">
        <Tabs items={tabItems} />
      </div>
      <Modal
        title={machinesFacade.infoUpload?.find((item) => item.docType === uploadForDoctype)?.docTypeName}
        centered
        open={!!uploadForDoctype}
        onCancel={() => {
          setUploadForDoctype(null);
          setPendingFile(null);
          setDescription('');
        }}
        keyboard
        destroyOnHidden
        okButtonProps={{
          disabled: !pendingFile,
          icon: <SaveOutlined />,
        }}
        okText="Lưu lại"
        onOk={handleUpload}
        cancelButtonProps={{ hidden: true }}
      >
        <div className="space-y-6">
          <div>
            <p className="pb-2">Mô tả ảnh</p>
            <Input.TextArea
              id="description"
              rows={4}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Mô tả ảnh được tải lên"
            />
          </div>
          <div>
            <p className="pb-2">Ảnh</p>
            <Upload
              onChange={(info) => {
                if (info.file.status === 'done') {
                  setPendingFile(info.file);
                }
              }}
              action={`https://api-monitor.autotimelapse.com/api/v1/upload/blob?destinationPhysicalPath=${uploadForDoctype ?? ''}`}
              listType="picture-card"
              className="[&_.ant-upload]:!flex-1"
              maxCount={1}
            >
              <UploadOutlined className="text-3xl" />
            </Upload>
          </div>
        </div>
      </Modal>
      {/* <Modal
        centered
        className="!w-auto"
        open={previewVisible}
        onCancel={() => {
          setPreviewVisible(false);
        }}
        footer={null}
        keyboard
        styles={{ body: { padding: 8 } }}
      >
        <div className="max-w-[calc(100vw-2rem)] max-h-[calc(100vh-3.5rem)] pt-11 grid grid-rows-[1fr_auto] [&_.ant-descriptions-item-label]:!w-32 gap-2">
          <div className="overflow-hidden rounded-xs">
            <img src={preview?.url || preview?.preview} alt="preview" className="object-contain size-full" />
          </div>
          {preview && (
            <Descriptions
              bordered
              column={1}
              items={[
                {
                  label: 'Ngày upload',
                  children: formatDayjsDate(preview.createdOnDate),
                },
                {
                  label: 'Mô tả',
                  children: preview.description,
                },
              ]}
            ></Descriptions>
          )}
        </div>
      </Modal> */}
    </>
  );
};

export default DeviceUploadPage;
