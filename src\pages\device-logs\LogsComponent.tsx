import { MachinesFacade } from '@store';
import { formatDayjsDate, uuidv4 } from '@utils';
import { DotLoading, InfiniteScroll, PullToRefresh } from 'antd-mobile';
import { BackTop, Button, DatePicker, Input } from 'antd';
import { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { FileTextOutlined, RedoOutlined, RetweetOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { RangePicker } = DatePicker;

export default function LogsComponent({ name, isMobile }: { name?: string; isMobile: boolean }) {
  const navigate = useNavigate();
  const machinesFacade = MachinesFacade();
  const sizeDeviceLogsRef = useRef(0);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [debouncedSearchKeyword, setDebouncedSearchKeyword] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [showBackToTop, setShowBackToTop] = useState(false);

  const fetchDeviceLogs = async (size?: number) => {
    if (!name) return;

    const params: {
      name: string;
      size?: number;
      keyword?: string;
      timeRanges?: string[];
    } = { name };

    if (size !== undefined) {
      params.size = size;
      sizeDeviceLogsRef.current = size;
    }

    if (debouncedSearchKeyword.trim()) {
      params.keyword = debouncedSearchKeyword.trim();
    }

    if (dateRange && dateRange[0] && dateRange[1]) {
      params.timeRanges = [dateRange[0].toISOString(), dateRange[1].toISOString()];
    }

    if (size === undefined || size === 0) {
      setHasMore(true);
      sizeDeviceLogsRef.current = 0;
    }

    await machinesFacade.getDeviceLogs(params);
  };

  useEffect(() => {
    const currentLogsCount = machinesFacade.deviceLogs?.length || 0;

    if (sizeDeviceLogsRef.current > 0 && currentLogsCount < sizeDeviceLogsRef.current) {
      setHasMore(false);
    } else if (sizeDeviceLogsRef.current > 0 && currentLogsCount > 0) {
      setHasMore(true);
    }
  }, [machinesFacade.deviceLogs]);

  // Debounce effect for search keyword
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchKeyword(searchKeyword);
    }, 1000);

    return () => clearTimeout(timer);
  }, [searchKeyword]);

  useEffect(() => {
    fetchDeviceLogs(20);
  }, [name, debouncedSearchKeyword, dateRange]);

  useEffect(() => {
    const handleScroll = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target && target.scrollTop !== undefined) {
        const scrollTop = target.scrollTop;
        const shouldShow = scrollTop > 300;
        setShowBackToTop(shouldShow);
      }
    };

    const scrollContainers = [document.querySelector('main')].filter(Boolean);

    scrollContainers.forEach((container) => {
      if (container) {
        container.addEventListener('scroll', handleScroll, { passive: true });
      }
    });
    return () => {
      scrollContainers.forEach((container) => {
        if (container) {
          container.removeEventListener('scroll', handleScroll);
        }
      });
    };
  }, []);

  const scrollToTop = () => {
    const scrollContainers = [document.querySelector('main')].filter(Boolean);

    scrollContainers.forEach((container) => {
      if (container && typeof container.scrollTo === 'function') {
        (container as any).scrollTo({ top: 0, behavior: 'smooth' });
      } else if (container) {
        (container as any).scrollTop = 0;
      }
    });
  };
  async function loadMoreDeviceLogs() {
    if (!hasMore) return;
    sizeDeviceLogsRef.current += 20;
    await fetchDeviceLogs(sizeDeviceLogsRef.current);
  }

  const handleRefresh = async () => {
    sizeDeviceLogsRef.current = 0;
    setHasMore(true);
    await fetchDeviceLogs();
  };

  const tableContent = (
    <table className="table-fixed w-full text-sm">
      <tbody>
        {machinesFacade.deviceLogs?.map((log, index) => (
          <tr key={uuidv4()} className={`${index % 2 === 0 ? 'bg-blue-50' : 'bg-white'} hover:bg-gray-50`}>
            <td className="px-2 py-1 w-27 text-gray-600 break-words">
              {formatDayjsDate(log.createdDate, 'HH:mm:ss DD/MM')}
            </td>
            <td className="px-2 py-1 break-words">{log.content}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );

  return (
    <>
      {isMobile && (
        <div className="p-4 bg-white ">
          <div className="mb-3">
            <Input.Search
              placeholder="Tìm kiếm logs..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              className="w-full"
              allowClear
            />
          </div>
          <div>
            <RangePicker
              placeholder={['Từ ngày', 'Đến ngày']}
              value={dateRange}
              onChange={(dates) => setDateRange(dates)}
              showTime={{ format: 'HH:mm:ss' }}
              format="DD/MM/YYYY HH:mm:ss"
              className="w-full"
              allowClear
            />
          </div>
        </div>
      )}

      {isMobile ? (
        <PullToRefresh
          canReleaseText="Thả để làm mới"
          completeText="Đã làm mới"
          pullingText="Kéo xuống để làm mới"
          refreshingText={
            <>
              <DotLoading /> Đang làm mới...
            </>
          }
          onRefresh={handleRefresh}
        >
          {tableContent}
        </PullToRefresh>
      ) : (
        tableContent
      )}

      {isMobile ? (
        <InfiniteScroll loadMore={loadMoreDeviceLogs} hasMore={hasMore}>
          {hasMore ? (
            <div className="text-center text-xs text-gray-400 py-2">
              <DotLoading /> Đang tải thêm...
            </div>
          ) : (
            <div className="text-center text-xs text-gray-400 py-2">Không còn dữ liệu</div>
          )}
        </InfiniteScroll>
      ) : (
        <div className="flex justify-center mt-4 gap-4 mb-5">
          <Button type="primary" onClick={handleRefresh}>
            <RedoOutlined /> Reload logs
          </Button>
          <Button type="primary" onClick={loadMoreDeviceLogs} disabled={!hasMore}>
            <RetweetOutlined /> Show more logs
          </Button>
          <Button type="primary" onClick={() => navigate(`/machine/${name}/file-logs`)}>
            <FileTextOutlined /> File logs
          </Button>
        </div>
      )}

      {isMobile && showBackToTop && (
        <div
          className="fixed bottom-14 right-5 z-50 bg-gray-400 text-white rounded-full p-3 shadow-lg cursor-pointer hover:bg-blue-700 transition-all duration-300"
          onClick={scrollToTop}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
        </div>
      )}
    </>
  );
}
