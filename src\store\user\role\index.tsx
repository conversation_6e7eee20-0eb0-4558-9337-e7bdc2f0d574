import { QueryParams, Responses } from '@models';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { Action, Slice, State, useAppDispatch, useTypedSelector } from '@store';
import { API } from '@utils';

const name = 'Role';
const action = {
  ...new Action<RolesModel>(name),
  getPermission: createAsyncThunk(name + '/permission', async () => API.get<Responses<string[]>>(`idm/roles`)),
};
export const roleSlice = createSlice(new Slice<RolesModel>(action, { keepUnusedDataFor: 9999 }));
export const RolesFacade = () => {
  const dispatch = useAppDispatch();
  return {
    ...useTypedSelector((state) => state[action.name] as StateRoles<RolesModel>),
    set: (values: StateRoles<RolesModel>) => dispatch(action.set(values)),
    get: (params: QueryParams) => dispatch(action.get(params)),
    getById: ({ id, keyState = 'isVisible' }: { id: string; keyState?: keyof StateRoles<RolesModel> }) =>
      dispatch(action.getById({ id, keyState })),
    post: (values: RolesModel) => dispatch(action.post({ values })),
    put: (values: RolesModel) => dispatch(action.put({ values })),
    putDisable: (values: { id: string; disable: boolean }) => dispatch(action.putDisable(values)),
    delete: (id: string) => dispatch(action.delete({ id })),
    getPermission: () => dispatch(action.getPermission()),
  };
};
interface StateRoles<T> extends State<T> {
  isCreate?: boolean;
}
export class RolesModel {
  constructor(
    public id?: string,
    public description?: string,
    public code?: string,
    public name?: string,
    public isSystem?: boolean,
    public level?: number,
    public value?: string,
    public label?: string,
  ) {}
}
