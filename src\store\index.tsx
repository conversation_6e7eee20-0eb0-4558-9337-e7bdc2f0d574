import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import {
  addressSlice,
  codeTypeManagementSlice,
  codeTypeSlice,
  dashboardSlice,
  dataSlice,
  dataTypeSlice,
  globalSlice,
  machinesSlice,
  navigationSlice,
  objMapUserSlice,
  parameterSlice,
  publicityLevelSlice,
  rightMapRoleSlice,
  roleSlice,
  typesCodeTypeManagementSlice,
  userSlice,
} from './';
import { Action } from './action';
import { Slice, State } from './slice';

const setupStore = () => {
  return configureStore({
    reducer: rootReducer,
  });
};
const useAppDispatch = () => useDispatch<ReturnType<typeof setupStore>['dispatch']>();
const useTypedSelector: TypedUseSelectorHook<ReturnType<typeof rootReducer>> = useSelector;
export { Action, setupStore, Slice, useAppDispatch, useTypedSelector };
export type { State };

export * from './address';
export * from './code';
export * from './code/type';
export * from './codetype';
export * from './dashboard';
export * from './data';
export * from './data/type';
export * from './global';
export * from './machines';
export * from './navigation';
export * from './obj-map-user';
export * from './parameter';
export * from './publicity-level';
export * from './right-map-role';
export * from './user';
export * from './user/role';

const rootReducer = combineReducers({
  [globalSlice.name]: globalSlice.reducer,
  [userSlice.name]: userSlice.reducer,
  [roleSlice.name]: roleSlice.reducer,
  [codeTypeManagementSlice.name]: codeTypeManagementSlice.reducer,
  [typesCodeTypeManagementSlice.name]: typesCodeTypeManagementSlice.reducer,
  [dataSlice.name]: dataSlice.reducer,
  [machinesSlice.name]: machinesSlice.reducer,
  [dataTypeSlice.name]: dataTypeSlice.reducer,
  [parameterSlice.name]: parameterSlice.reducer,
  [navigationSlice.name]: navigationSlice.reducer,
  [objMapUserSlice.name]: objMapUserSlice.reducer,
  [addressSlice.name]: addressSlice.reducer,
  [codeTypeSlice.name]: codeTypeSlice.reducer,
  [publicityLevelSlice.name]: publicityLevelSlice.reducer,
  [rightMapRoleSlice.name]: rightMapRoleSlice.reducer,
  [dashboardSlice.name]: dashboardSlice.reducer,
});
