// header {
//   .ant-select:not(.ant-select-customize-input) {
//     .ant-select-selector {
//       @apply bg-teal-900 border-teal-900 text-white rounded-full;
//     }
//     .ant-select-arrow {
//       @apply text-white;
//     }
//   }
// }
// .hamburger {
//   @apply inline-block -right-5 absolute transition-all duration-300 ease-in-out z-10 cursor-pointer w-7;

//   .line {
//     @apply bg-gray-600 block transition-all duration-300 ease-in-out rounded-sm h-1 ~"my-1.5";

//     &:nth-child(1),
//     &:nth-child(2) {
//       @apply w-full;
//     }
//     &:nth-child(3) {
//       @apply w-1/2;
//     }
//   }

//   &.is-active {
//     .line {
//       &:nth-child(1),
//       &:nth-child(3) {
//         @apply h-1 w-1/2;
//       }
//       &:nth-child(2) {
//         @apply transform translate-x-0 h-1 w-full;
//       }
//       &:nth-child(1) {
//         @apply translate-y-1 ~"translate-x-3.5" rotate-45 w-5;
//       }
//       &:nth-child(3) {
//         @apply -translate-y-1 ~"translate-x-3.5" -rotate-45 w-5;
//       }
//     }
//   }
//   &:not(.is-active):hover .line {
//     &:nth-child(1) {
//       @apply animate-burger-hover-2;
//     }
//     &:nth-child(2) {
//       @apply animate-burger-hover-4;
//     }
//     &:nth-child(3) {
//       @apply animate-burger-hover-6;
//     }
//   }
// }

// .icon-menu {
//   @apply !h-8 !w-8;
// }
// .icon-header {
//   @apply h-9 w-9 fill-gray-600;
// }
// .icon-dashboard {
//   @apply h-6 w-6;
// }
// .ant-tabs-nav {
//   @apply !mb-0 !bg-transparent;
//   .ant-tabs-tab {
//     @apply !border-none;
//   }
// }
// .tab-wrapper {
//   .ant-tabs-nav {
//     &::before {
//       @apply !border-none;
//     }
//   }
// }
// .ant-collapse > .ant-collapse-item > .ant-collapse-header {
//   padding: 12px 8px;
//   flex-direction: row-reverse;
//   align-items: center;
// }

// .ant-collapse-expand-icon {
//   @apply h-10 text-gray-700;
// }

// .profile {
//   .ant-tabs-tab {
//     @apply !bg-transparent;

//     &:hover {
//       @apply !text-gray-400;
//     }
//     .ant-tabs-tab-btn {
//       @apply text-xl !text-gray-400 font-semibold;
//     }
//   }
//   .ant-tabs-nav {
//     @apply !bg-white !pl-5 lg:!rounded-t-2xl;
//   }

//   .ant-tabs-tab-active {
//     .ant-tabs-tab-btn {
//       @apply text-xl !text-green-800 font-semibold;
//     }
//   }
//   .flex.gap-2.mb-2 {
//     @apply justify-center;
//   }
//   .w-24 {
//     @apply w-40 h-40 mx-auto rounded-xl;
//   }
// }
