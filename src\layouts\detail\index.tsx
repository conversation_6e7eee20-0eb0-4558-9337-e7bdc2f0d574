import { GlobalFacade, MachinesFacade } from '@store';
import { Flex, Layout, theme } from 'antd';
import { NavBar, TabBar } from 'antd-mobile';
import { AppOutline, LocationOutline } from 'antd-mobile-icons';
import { Content } from 'antd/es/layout/layout';
import React, { ReactNode, useEffect, useState } from 'react';
import { Outlet, useLocation, useNavigate, useParams } from 'react-router';
import { useSearchParams } from 'react-router-dom';
import { useIsMobile } from 'src/utils/hooks/useIsMobile';
import {
  SettingOutlined,
  LineChartOutlined,
  UploadOutlined,
  VideoCameraOutlined,
  ContainerOutlined,
  FileZipOutlined,
} from '@ant-design/icons';

export type T_MasterCtx = {
  tool: [ReactNode, (tool: ReactNode) => void];
  breadCrumb?: ReactNode;
  visible?: boolean;
  machineName: [string, (name: string) => void];
};

const MasterLayout = () => {
  const [tool, setTool] = useState<ReactNode>(null);
  const { name } = useParams();
  const [machineName, setMachineName] = useState<string>(name || '');
  const [tabUrl, setTabUrl] = useState('');
  const machinesFacade = MachinesFacade();
  const navigate = useNavigate();
  const location = useLocation();
  const currentTab = location.pathname.split('/').pop();
  const isMobile = useIsMobile();
  const outletCtx: T_MasterCtx = {
    tool: [tool, setTool],
    machineName: [machineName, setMachineName],
  };
  window.scrollTo({ top: 0, behavior: 'smooth' });

  const tabs = [
    {
      key: `/machine/${machineName}/configuration`,
      title: 'Thiết bị',
      icon: <SettingOutlined />,
      tabValue: 'configuration',
    },
    {
      key: `/machine/${machineName}/logs`,
      title: 'Online Logs',
      icon: <ContainerOutlined />,
      tabValue: 'logs',
    },
    {
      key: `/machine/${machineName}/file-logs`,
      title: 'File Logs',
      icon: <FileZipOutlined />,
      tabValue: 'file-logs',
    },
    {
      key: `/machine/${machineName}/chart`,
      title: 'Biểu đồ',
      icon: <LineChartOutlined />,
      tabValue: 'chart',
    },
    {
      key: `/machine/${machineName}/upload`,
      title: 'Tải tệp lên ',
      icon: <UploadOutlined />,
      tabValue: 'upload',
    },
  ];

  const visibleTabs = isMobile ? tabs : tabs.filter((tab) => tab.tabValue !== 'logs');

  // Find the current tab title
  const currentTabTitle = tabs.find((tab) => tab.tabValue === currentTab)?.title || 'Thiết bị';

  useEffect(() => {
    machinesFacade.set({ currentTabDetail: `/machine/${machineName}/${currentTab}` });
    console.log(`/machine/${machineName}/${currentTab}`);
  }, []);

  return (
    <Layout className="!h-screen flex" style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <Content style={{ flex: 1, overflow: 'auto' }} className={'w-full overflow-auto miniScroll'}>
        <NavBar className="bg-white !h-20 fixed top-0 left-0 right-0 z-50" onBack={() => navigate('/machines')}>
          <div className="text-xl font-semibold">{currentTabTitle}</div>
          <div className="my-nav-bar-subtitle text-sm text-blue-500">{machineName}</div>
        </NavBar>
        <div className="pt-20">
          <Outlet context={outletCtx} />
        </div>
      </Content>
      {isMobile && (
        <TabBar
          className="bg-white shadow-header sticky bottom-0 left-0 right-0 [&>*]:mt-1.5"
          safeArea
          onChange={(key) => {
            navigate(key);
            machinesFacade.set({ currentTabDetail: key });
          }}
          activeKey={machinesFacade.currentTabDetail}
          defaultActiveKey={`/machine/${machineName}/configuration`}
        >
          {visibleTabs.map((item) => (
            <TabBar.Item key={item.key} icon={item.icon} title={item.title} />
          ))}
        </TabBar>
      )}
    </Layout>
  );
};

export const SubHeader = (props: {
  children?: React.ReactNode;
  tool?: React.ReactNode;
  breadcrumb?: string;
  isVisible?: boolean;
}) => {
  return (
    <Flex justify="space-between" className="sticky top-0 z-50 !px-3 shadow-header bg-white min-h-12" align="center">
      <Flex align="center" gap={10}>
        {props.tool}
      </Flex>
    </Flex>
  );
};

export default MasterLayout;
