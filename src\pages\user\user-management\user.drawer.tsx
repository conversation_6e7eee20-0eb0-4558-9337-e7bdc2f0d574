import { CloseOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { EStatusState } from '@models';
import { GlobalFacade, RolesFacade, RolesModel, UserFacade, UserModel } from '@store';
import { Badge, Button, Drawer, Form, Input, Select, Space, Tooltip } from 'antd';
import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import UserGroupEditForm from '../user-group-management/edit.drawer';

export const UserDrawer = () => {
  const [form] = Form.useForm();
  const userFacade = UserFacade();
  const rolesFacade = RolesFacade();
  const { changePasswordProfile } = GlobalFacade();
  const [searchParams, setSearchParams] = useSearchParams();
  const {
    page,
    size,
    filter = '{}',
    sort = '',
    id = '',
  } = {
    ...Object.fromEntries(searchParams),
    page: Number(searchParams.get('page') || 1),
    size: Number(searchParams.get('size') || 20),
    id: searchParams.get('id') || '',
  };

  useEffect(() => {
    rolesFacade.get({});
  }, []);

  useEffect(() => {
    if ((userFacade.isEdit && id) || (userFacade.isDetail && id)) {
      userFacade.getById({ id });
    }
  }, [id, userFacade.isEdit, userFacade.isDetail]);

  useEffect(() => {
    switch (rolesFacade.status) {
      case EStatusState.postFulfilled:
        rolesFacade.get({});
        form.setFieldValue('roleListCode', [rolesFacade.data?.code]);
        break;
    }
  }, [rolesFacade.status]);

  useEffect(() => {
    switch (userFacade.status) {
      case EStatusState.getByIdFulfilled:
        for (const key in userFacade.data) {
          form.setFieldValue(key, userFacade.data?.[key as keyof UserModel]);
        }
        break;
    }
  }, [userFacade.status]);

  const onFinish = (values: any) => {
    if (userFacade.isEdit && id) {
      return userFacade.put({ ...values, id });
    } else if (userFacade.isPassword && id) {
      return changePasswordProfile({ id, values });
    } else return userFacade.post({ ...values });
  };

  const handleCloseDrawer = () => {
    userFacade.set({ isEdit: false, isCreate: false, isDetail: false, isPassword: false });
  };

  const RenderValues = ({ lable, value }: { lable: string; value: React.ReactNode }) => (
    <div className="flex mb-4">
      <p className="w-36">{lable}</p>
      <div className="font-semibold">{value}</div>
    </div>
  );
  const renderTitle = () => {
    if (userFacade.isEdit) {
      return 'Chỉnh sửa người dùng';
    } else if (userFacade.isDetail) {
      return 'Xem chi tiết người dùng';
    } else if (userFacade.isPassword) {
      return 'Đổi mật khẩu người dùng';
    } else {
      return 'Thêm mới người dùng';
    }
  };

  return (
    <>
      <Drawer
        loading={userFacade.isLoading}
        title={renderTitle()}
        width={450}
        open={userFacade.isCreate || userFacade.isEdit || userFacade.isDetail || userFacade.isPassword}
        onClose={handleCloseDrawer}
        maskClosable={false}
        closeIcon={false}
        afterOpenChange={(visible) => {
          if (!visible) {
            form.resetFields();
            setSearchParams(
              (prev) => {
                prev.delete('id');
                return prev;
              },
              { replace: true },
            );
          }
        }}
        extra={<Button icon={<CloseOutlined />} type={'text'} onClick={handleCloseDrawer} />}
        footer={
          <Space className={'flex justify-end'}>
            <Button type={'default'} block onClick={handleCloseDrawer}>
              Huỷ bỏ
            </Button>
            <Button
              className={`${userFacade.isDetail ? 'hidden' : 'block'}`}
              type={'primary'}
              block
              onClick={form.submit}
            >
              Lưu lại
            </Button>
          </Space>
        }
      >
        <Form form={form} layout={'vertical'} onFinish={onFinish}>
          {userFacade.isDetail ? (
            <>
              <RenderValues
                lable={'Nhóm người dùng:'}
                value={userFacade.data?.listRole?.map((item: RolesModel) => <p key={item.id}>{item.name}</p>)}
              />
              <RenderValues lable={'Tên tài khoản:'} value={userFacade.data?.name ?? '---'} />
              <RenderValues lable={'Số điện thoại:'} value={userFacade.data?.phoneNumber ?? '---'} />
              <RenderValues lable={'Email:'} value={userFacade.data?.email ?? '---'} />
              <RenderValues
                lable={'Mật khẩu:'}
                value={<Input.Password variant="borderless" value={userFacade.data?.plainTextPwd} />}
              />
              <RenderValues
                lable={'Trạng thái tài khoản:'}
                value={
                  <Badge
                    color={!userFacade.data?.isLockedOut ? 'green' : ''}
                    status={userFacade.data?.isLockedOut === true ? 'error' : 'processing'}
                    text={userFacade.data?.isLockedOut === true ? 'Đã khóa' : 'Đang hoạt động'}
                  />
                }
              />
            </>
          ) : userFacade.isPassword ? (
            <>
              <Form.Item
                name="password"
                label="Mật khẩu mới"
                rules={[
                  { required: true, message: 'Mật khẩu mới là bắt buộc!' },
                  {
                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/,
                    message: 'Mật khẩu mới cần có ít nhất 8 ký tự, gồm chữ hoa, chữ thường, số và ký tự đặc biệt!',
                  },
                ]}
                hasFeedback
              >
                <Input.Password placeholder="Nhập mật khẩu mới" />
              </Form.Item>

              <Form.Item
                name="confirm"
                label="Xác nhận mật khẩu mới"
                dependencies={['password']}
                hasFeedback
                rules={[
                  { required: true, message: 'Xác nhận mật khẩu mới là bắt buộc!' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('Mật khẩu mới không khớp!'));
                    },
                  }),
                ]}
              >
                <Input.Password placeholder="Nhập lại mật khẩu mới" />
              </Form.Item>
            </>
          ) : (
            <>
              <Form.Item
                name="roleListCode"
                label="Nhóm người dùng"
                rules={[{ required: true, message: 'Nhóm người dùng là bắt buộc!' }]}
              >
                <Select
                  suffixIcon={
                    <Tooltip title="Thêm mới nhóm người dùng">
                      <PlusCircleOutlined
                        className="!text-green-600"
                        onClick={() => rolesFacade.set({ isCreate: true })}
                      />
                    </Tooltip>
                  }
                  options={rolesFacade.pagination?.content.map((item) => ({ label: item.name, value: item.code }))}
                  placeholder={'Chọn nhóm người dùng'}
                  mode="multiple"
                  optionFilterProp="label"
                />
              </Form.Item>
              <Form.Item name="code" label="Mã người dùng">
                <Input placeholder={'Nhập mã'} />
              </Form.Item>
              <Form.Item name="name" label="Họ và tên" rules={[{ required: true, message: 'Họ và tên là bắt buộc!' }]}>
                <Input placeholder={'Nhập họ và tên'} />
              </Form.Item>
              <Form.Item name="email" label="Email" rules={[{ type: 'email', message: 'E-mail không hợp lệ!' }]}>
                <Input placeholder={'Nhập email'} />
              </Form.Item>
              <Form.Item
                name="phoneNumber"
                label="Số điện thoại"
                rules={[
                  { required: true, message: 'Số điện thoại là bắt buộc!' },
                  {
                    pattern: /^[0-9]{9,10}$/,
                    message: 'Số điện thoại không đúng định dạng',
                  },
                ]}
              >
                <Input placeholder={'Nhập số điện thoại'} />
              </Form.Item>
              {!userFacade.isEdit && (
                <Form.Item
                  name="password"
                  label="Mật khẩu"
                  rules={[
                    { required: true, message: 'Mật khẩu là bắt buộc!' },
                    {
                      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/,
                      message: 'Mật khẩu cần có ít nhất 8 ký tự, gồm chữ hoa, chữ thường, số và ký tự đặc biệt!',
                    },
                  ]}
                >
                  <Input.Password placeholder={'Nhập mật khẩu'} />
                </Form.Item>
              )}
            </>
          )}
        </Form>
      </Drawer>
      <UserGroupEditForm />
    </>
  );
};
