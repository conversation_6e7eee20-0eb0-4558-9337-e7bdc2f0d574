@import 'tailwindcss';
@import 'suneditor/dist/css/suneditor.min.css';

@theme {
  --shadow-header: 0 0.125rem 0.25rem rgba(168, 168, 168, 0.25);
}

@layer base {
  /* @font-face {
    font-family: Lexend;
    src: url(/assets/fonts/Lexend-VariableFont_wght.ttf) format('truetype');
  } */
}

body {
  @apply text-sm overflow-x-hidden text-gray-700 bg-gray-100;
}

/* <PERSON><PERSON><PERSON> lập vòng lặp LESS bằng cách viết thủ công */
.intro-x:nth-child(9),
.-intro-x:nth-child(9) {
  transform: translateX(3rem); /* 12 * 0.25rem = 3rem */
  opacity: 0;
  animation: intro-x-animation 0.3s ease forwards;
  z-index: 1;
  animation-delay: 0.9s;
}

.intro-x:nth-child(8),
.-intro-x:nth-child(8) {
  transform: translateX(3rem);
  opacity: 0;
  animation: intro-x-animation 0.3s ease forwards;
  z-index: 2;
  animation-delay: 0.8s;
}

.intro-x:nth-child(7),
.-intro-x:nth-child(7) {
  transform: translateX(3rem);
  opacity: 0;
  animation: intro-x-animation 0.3s ease forwards;
  z-index: 3;
  animation-delay: 0.7s;
}

.intro-x:nth-child(6),
.-intro-x:nth-child(6) {
  transform: translateX(3rem);
  opacity: 0;
  animation: intro-x-animation 0.3s ease forwards;
  z-index: 4;
  animation-delay: 0.6s;
}

.intro-x:nth-child(5),
.-intro-x:nth-child(5) {
  transform: translateX(3rem);
  opacity: 0;
  animation: intro-x-animation 0.3s ease forwards;
  z-index: 5;
  animation-delay: 0.5s;
}

.intro-x:nth-child(4),
.-intro-x:nth-child(4) {
  transform: translateX(3rem);
  opacity: 0;
  animation: intro-x-animation 0.3s ease forwards;
  z-index: 6;
  animation-delay: 0.4s;
}

.intro-x:nth-child(3),
.-intro-x:nth-child(3) {
  transform: translateX(3rem);
  opacity: 0;
  animation: intro-x-animation 0.3s ease forwards;
  z-index: 7;
  animation-delay: 0.3s;
}

.intro-x:nth-child(2),
.-intro-x:nth-child(2) {
  transform: translateX(3rem);
  opacity: 0;
  animation: intro-x-animation 0.3s ease forwards;
  z-index: 8;
  animation-delay: 0.2s;
}

.intro-x:nth-child(1),
.-intro-x:nth-child(1) {
  transform: translateX(3rem);
  opacity: 0;
  animation: intro-x-animation 0.3s ease forwards;
  z-index: 9;
  animation-delay: 0.1s;
}

/* Nếu cần phiên bản .-intro-x ngược chiều */
.-intro-x:nth-child(9) {
  transform: translateX(-3rem);
}
.-intro-x:nth-child(8) {
  transform: translateX(-3rem);
}
.-intro-x:nth-child(7) {
  transform: translateX(-3rem);
}
.-intro-x:nth-child(6) {
  transform: translateX(-3rem);
}
.-intro-x:nth-child(5) {
  transform: translateX(-3rem);
}
.-intro-x:nth-child(4) {
  transform: translateX(-3rem);
}
.-intro-x:nth-child(3) {
  transform: translateX(-3rem);
}
.-intro-x:nth-child(2) {
  transform: translateX(-3rem);
}
.-intro-x:nth-child(1) {
  transform: translateX(-3rem);
}

/* Định nghĩa animation */
@keyframes intro-x-animation {
  0% {
    opacity: 0;
    transform: translateX(3rem);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* .ant-layout-header {
  @apply flex items-center justify-between h-13 px-2;
}

#ant-sider-logo {
  @apply flex items-center justify-center h-13 py-2 px-4;
}

.ant-layout-sider-trigger {
  @apply !h-13;
} */

.ant-form-item-explain-error {
  @apply text-xs italic;
}

.miniScroll {
  &::-webkit-scrollbar {
    width: 0.1875rem;
  }
  &::-webkit-scrollbar-track {
    background: #eeebeb;
    border-radius: 0.25rem;
  }
  &::-webkit-scrollbar-thumb {
    background: #bbbbbb;
    border-radius: 0.25rem;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #a1a0a0;
  }
}

.ant-table-body {
  scrollbar-width: thin;
}

.ant-drawer-body {
  scrollbar-width: thin;
  @apply !overflow-auto;
}

.ant-picker-panel-container {
  .ant-picker-presets {
    min-height: 25rem !important;
  }
  @media (max-width: 50rem) {
    overflow: scroll !important;
    height: 25rem;
    .ant-picker-panel-layout {
      flex-direction: column !important;

      .ant-picker-presets {
        max-width: 100% !important;
        min-height: 10rem !important;
      }

      .ant-picker-panels,
      .ant-picker-datetime-panel {
        flex-direction: column !important;
      }
    }
  }
}
