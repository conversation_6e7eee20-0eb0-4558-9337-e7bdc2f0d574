import { keyToken, lang, routerLinks } from '@utils';
import { Spin } from 'antd';
import React, { Suspense } from 'react';
import { HashRouter, Navigate, Outlet, Route, Routes } from 'react-router-dom';

const pages = [
  {
    layout: React.lazy(() => import('@layouts/guest')),
    isPublic: true,
    child: [],
  },
  {
    layout: React.lazy(() => import('@layouts/auth')),
    isPublic: true,
    child: [
      {
        path: routerLinks('Login'),
        component: React.lazy(() => import('@pages/auth/login')),
      },
      {
        path: routerLinks('Register'),
        component: React.lazy(() => import('@pages/auth/register')),
      },
      {
        path: routerLinks('ForgetPassword'),
        component: React.lazy(() => import('@pages/forget-password')),
      },
      {
        path: routerLinks('VerifyForotPassword'),
        component: React.lazy(() => import('@pages/forget-password/otp')),
      },
      {
        path: routerLinks('SetPassword'),
        component: React.lazy(() => import('@pages/forget-password/otp/set-password')),
      },
    ],
  },
  {
    layout: React.lazy(() => import('@layouts/admin')),
    isPublic: false,
    child: [
      {
        path: '/',
        component: routerLinks('Machines'),
      },
      {
        path: '/quick-view',
        component: React.lazy(() => import('@pages/machines/components/MachineQuickList')),
      },
      {
        path: '/user-permission',
        component: React.lazy(() => import('@pages/user-permission')),
      },
      {
        path: routerLinks('RightMapRole'),
        component: React.lazy(() => import('@pages/right-map-role')),
      },
      {
        path: routerLinks('Role'),
        component: React.lazy(() => import('@pages/user/user-group-management')),
      },
      {
        path: routerLinks('User'),
        component: React.lazy(() => import('@pages/user/user-management')),
      },
      {
        path: routerLinks('MyProfile'),
        component: React.lazy(() => import('@pages/my-profile')),
      },
      {
        path: routerLinks('Machines'),
        component: React.lazy(() => import('@pages/machines')),
      },
      {
        path: routerLinks('GPSMachine'),
        component: React.lazy(() => import('@pages/machine-gps')),
      },
      {
        path: routerLinks('Navigation'),
        component: React.lazy(() => import('@pages/navigation')),
      },
      {
        path: routerLinks('CodeType'),
        component: React.lazy(() => import('@pages/codetype')),
      },
      {
        path: routerLinks('PageNotFound'),
        component: React.lazy(() => import('@pages/PageNotFound')),
      },
    ], // 💬 generate link to here
  },
  {
    layout: React.lazy(() => import('@layouts/detail')),
    isPublic: false,
    child: [
      {
        path: routerLinks('DetailMachine') + '/:name/configuration',
        component: React.lazy(() => import('@pages/machines/detail')),
      },
      {
        path: routerLinks('DetailMachine') + '/:name/logs',
        component: React.lazy(() => import('@pages/device-logs')),
      },
      {
        path: routerLinks('DetailMachine') + '/:name/file-logs',
        component: React.lazy(() => import('@pages/file-logs')),
      },
      {
        path: routerLinks('DetailMachine') + '/:name/upload',
        component: React.lazy(() => import('@pages/device-upload')),
      },
      {
        path: routerLinks('DetailMachine') + '/:name/chart',
        component: React.lazy(() => import('@pages/device-charts')),
      },
    ],
  },
];

const Layout = ({
  layout: MasterLayout,
  isPublic = false,
}: {
  layout: React.LazyExoticComponent<({ children }: { children?: React.ReactNode }) => JSX.Element>;
  isPublic: boolean;
}) => {
  if (isPublic || !!localStorage.getItem(keyToken))
    return (
      <MasterLayout>
        <Outlet />
      </MasterLayout>
    );

  return <Navigate to={`${routerLinks('Login')}`} />;
};

const Page = ({
  component: Comp,
}: {
  component: React.LazyExoticComponent<() => JSX.Element> | React.LazyExoticComponent<React.FC<any>>;
}) => <Comp />;
const Pages = () => {
  return (
    <>
      <HashRouter>
        <Routes>
          <Route path={'/'}>
            {pages.map(({ layout, isPublic, child }, index) => (
              // <Route key={index} element={<MasterLayout isPublic={isPublic} />}>
              <Route key={index} element={<Layout layout={layout} isPublic={isPublic} />}>
                {child.map(({ path = '', component }, subIndex: number) => (
                  <Route
                    key={path + subIndex}
                    path={'/' + path}
                    element={
                      <Suspense
                        fallback={
                          <Spin>
                            <div className="!w-screen !h-screen" />
                          </Spin>
                        }
                      >
                        {typeof component === 'string' ? (
                          <Navigate to={component} />
                        ) : (
                          <Page component={component} key={path} />
                        )}
                      </Suspense>
                    }
                  />
                ))}
              </Route>
            ))}
          </Route>
          <Route path="*" element={<Navigate to={routerLinks('PageNotFound')} />} />
        </Routes>
      </HashRouter>
    </>
  );
};

export default Pages;
