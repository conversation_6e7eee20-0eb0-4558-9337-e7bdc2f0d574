import { Dayjs } from 'dayjs';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { CommonEntity, Pagination, QueryParams } from '@models';
import { Action, Slice, State, useAppDispatch, useTypedSelector } from '@store';
import { API, routerLinks } from '@utils';

const name = 'Dashboard';
const action = {
  ...new Action<DashboardModel, EStatusDashboard>(name),
  getDeliveryOrderSummary: createAsyncThunk(name + 'getDeliveryOrderSummary', async () => {
    return await API.get(`${routerLinks(name, 'api')}/delivery-order-summary`);
  }),
  getDeliveryOrderDebtSummary: createAsyncThunk(name + 'getDeliveryOrderDebtSummary', async (params: QueryParams) => {
    return await API.get(`${routerLinks(name, 'api')}/delivery-order-debt-summary`, params);
  }),
  getDeliveryOrderCollectPaid: createAsyncThunk(name + 'getDeliveryOrderCollectPaid', async (params: QueryParams) => {
    return await API.get(`${routerLinks(name, 'api')}/delivery-order-collect-paid`, params);
  }),
  getDeliveryOrderPaidRate: createAsyncThunk(name + 'getDeliveryOrderPaidRate', async (params: QueryParams) => {
    return await API.get(`${routerLinks(name, 'api')}/delivery-order-paid-rate`, params);
  }),
  getDeliveryOrderCollectDataSummary: createAsyncThunk(
    name + 'getDeliveryOrderCollectDataSummary',
    async (params: QueryParams) => {
      return await API.get(`${routerLinks(name, 'api')}/delivery-order-collect-data-summary`, params);
    },
  ),
};
export const dashboardSlice = createSlice(
  new Slice<DashboardModel, EStatusDashboard>(action, {}, (builder) => {
    builder
      .addCase(action.getDeliveryOrderSummary.pending, (state) => {
        state.isLoading = true;
        state.status = EStatusDashboard.getDeliveryOrderSummaryPending;
      })
      .addCase(action.getDeliveryOrderSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.status = EStatusDashboard.getDeliveryOrderSummaryFulfilled;
        state.data = action.payload.data as DeliveryOrderSummaryViewModel;
      })
      .addCase(action.getDeliveryOrderSummary.rejected, (state) => {
        state.isLoading = false;
        state.status = EStatusDashboard.getDeliveryOrderSummaryRejected;
      })
      .addCase(action.getDeliveryOrderDebtSummary.pending, (state) => {
        state.isLoading = true;
        state.status = EStatusDashboard.getDeliveryOrderDebtSummaryPending;
      })
      .addCase(action.getDeliveryOrderDebtSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.status = EStatusDashboard.getDeliveryOrderDebtSummaryFulfilled;
        state.deliveryOrderDebtSummary = action.payload.data as DeliveryOrderDebtSummaryViewModel;
      })
      .addCase(action.getDeliveryOrderDebtSummary.rejected, (state) => {
        state.isLoading = false;
        state.status = EStatusDashboard.getDeliveryOrderDebtSummaryRejected;
      })
      .addCase(action.getDeliveryOrderCollectPaid.pending, (state) => {
        state.isLoading = true;
        state.status = EStatusDashboard.getDeliveryOrderCollectPaidPending;
      })
      .addCase(action.getDeliveryOrderCollectPaid.fulfilled, (state, action) => {
        state.isLoading = false;
        state.status = EStatusDashboard.getDeliveryOrderCollectPaidFulfilled;
        state.deliveryOrderCollectPaid = action.payload.data as any;
      })
      .addCase(action.getDeliveryOrderCollectPaid.rejected, (state) => {
        state.isLoading = false;
        state.status = EStatusDashboard.getDeliveryOrderCollectPaidRejected;
      })
      .addCase(action.getDeliveryOrderPaidRate.pending, (state) => {
        state.isLoading = true;
        state.status = EStatusDashboard.getDeliveryOrderPaidRatePending;
      })
      .addCase(action.getDeliveryOrderPaidRate.fulfilled, (state, action) => {
        state.isLoading = false;
        state.status = EStatusDashboard.getDeliveryOrderPaidRateFulfilled;
        state.deliveryOrderPaidRate = action.payload.data as DeliveryOrderCollectPaidSummaryViewModel;
      })
      .addCase(action.getDeliveryOrderPaidRate.rejected, (state) => {
        state.isLoading = false;
        state.status = EStatusDashboard.getDeliveryOrderPaidRateRejected;
      })
      .addCase(action.getDeliveryOrderCollectDataSummary.pending, (state) => {
        state.isLoading = true;
        state.status = EStatusDashboard.getDeliveryOrderCollectDataSummaryPending;
      })
      .addCase(action.getDeliveryOrderCollectDataSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.status = EStatusDashboard.getDeliveryOrderCollectDataSummaryFulfilled;
        state.deliveryOrderCollectDataSummary = action.payload.data as DeliveryOrderCollectDataSummaryViewModel;
      })
      .addCase(action.getDeliveryOrderCollectDataSummary.rejected, (state) => {
        state.isLoading = false;
        state.status = EStatusDashboard.getDeliveryOrderCollectDataSummaryRejected;
      });
  }),
);
export const DashboardFacade = () => {
  const dispatch = useAppDispatch();
  return {
    ...useTypedSelector((state) => state[action.name] as StateDashboard<DashboardModel>),
    set: (values: StateDashboard<DashboardModel>) => dispatch(action.set(values)),
    getDeliveryOrderSummary: () => dispatch(action.getDeliveryOrderSummary()),
    getDeliveryOrderDebtSummary: (params: QueryParams) => dispatch(action.getDeliveryOrderDebtSummary(params)),
    getDeliveryOrderCollectPaid: (params: QueryParams) => dispatch(action.getDeliveryOrderCollectPaid(params)),
    getDeliveryOrderPaidRate: (params: QueryParams) => dispatch(action.getDeliveryOrderPaidRate(params)),
    getDeliveryOrderCollectDataSummary: (params: QueryParams) =>
      dispatch(action.getDeliveryOrderCollectDataSummary(params)),
  };
};
interface StateDashboard<T> extends State<T, EStatusDashboard> {
  deliveryOrderDebtSummary: DeliveryOrderDebtSummaryViewModel;
  deliveryOrderCollectPaid: Pagination<any>;
  deliveryOrderPaidRate: DeliveryOrderCollectPaidSummaryViewModel;
  deliveryOrderCollectDataSummary: DeliveryOrderCollectDataSummaryViewModel;
}

export class DashboardModel extends CommonEntity {
  constructor(
    public deliveryOrderPaid: DeliveryOrderPaidSummaryViewModel,
    public deliveryOrderCollect: DeliveryOrderCollectSummaryViewModel,
  ) {
    super();
  }
}

export enum EStatusDashboard {
  getDeliveryOrderSummaryPending = 'getDeliveryOrderSummary.pending',
  getDeliveryOrderSummaryFulfilled = 'getDeliveryOrderSummary.fulfilled',
  getDeliveryOrderSummaryRejected = 'getDeliveryOrderSummary.rejected',

  getDeliveryOrderDebtSummaryPending = 'getDeliveryOrderDebtSummary.pending',
  getDeliveryOrderDebtSummaryFulfilled = 'getDeliveryOrderDebtSummary.fulfilled',
  getDeliveryOrderDebtSummaryRejected = 'getDeliveryOrderDebtSummary.rejected',

  getDeliveryOrderCollectPaidPending = 'getDeliveryOrderCollectPaid.pending',
  getDeliveryOrderCollectPaidFulfilled = 'getDeliveryOrderCollectPaid.fulfilled',
  getDeliveryOrderCollectPaidRejected = 'getDeliveryOrderCollectPaid.rejected',

  getDeliveryOrderPaidRatePending = 'getDeliveryOrderPaidRate.pending',
  getDeliveryOrderPaidRateFulfilled = 'getDeliveryOrderPaidRate.fulfilled',
  getDeliveryOrderPaidRateRejected = 'getDeliveryOrderPaidRate.rejected',

  getDeliveryOrderCollectDataSummaryPending = 'getDeliveryOrderCollectDataSummary.pending',
  getDeliveryOrderCollectDataSummaryFulfilled = 'getDeliveryOrderCollectDataSummary.fulfilled',
  getDeliveryOrderCollectDataSummaryRejected = 'getDeliveryOrderCollectDataSummary.rejected',
}

export interface DeliveryOrderSummaryViewModel {
  deliveryOrderPaid: DeliveryOrderPaidSummaryViewModel;
  deliveryOrderCollect: DeliveryOrderCollectSummaryViewModel;
}

// model số lượng đơn đã thanh toán
interface DeliveryOrderPaidSummaryViewModel {
  totalDeliveryOrder: number; // Tổng số lượng đơn vận chuyển
  totalDeliveryOrderCollect: number; // Tổng số lượng đơn thu hộ
}

// model tổng giá trị đơn thu hộ
interface DeliveryOrderCollectSummaryViewModel {
  totalDeliveryOrderCollectPaid: number; // Tổng giá trị đơn thu hộ đã thanh toán
  totalDeliveryOrderCollectUnpaid: number; // Tổng giá trị đơn thu hộ chưa thanh toán
}

// model Tổng tiền nợ
export interface DeliveryOrderDebtSummaryViewModel {
  totalDebtAmount: number; // Tổng tiền nợ
  totalDebtPaidAmount: number; // Tổng tiền đã trả
  totalDebtUnpaidAmount: number; // Tổng tiền chưa thanh toán
}

// model tổng giá trị đơn thu hộ đã thanh toán
export interface DeliveryOrderCollectPaidSummaryViewModel {
  deliveryOrderPaidRate: number; // Tổng giá trị đơn thu hộ đã thanh toán
  deliveryOrderUnpaidRate: number; // Tổng giá trị đơn thu hộ chưa thanh toán
  deliveryOrderPartiallyPaidRate: number; // Tỷ lệ đơn thu hộ đã thanh toán
}

interface DeliveryOrderCollectDataSummaryViewModel {
  categories: string[]; // Danh sách các danh mục
  series: {
    name: string; // Tên của chuỗi dữ liệu
    data: number[]; // Dữ liệu cho từng danh mục
    color: 'green' | 'red'; // Màu sắc của chuỗi dữ liệu
  }[]; // Danh sách các chuỗi dữ liệu
}
