import { MachinesFacade } from '@store';
import { useEffect, useRef } from 'react';
import { useParams } from 'react-router';
import { formatDayjsDate } from '@utils';
import { Progress, Table, Typography } from 'antd';
import { Card, Collapse, DotLoading, Grid, List, Popover, PullToRefresh, Space, SwipeAction, Tag } from 'antd-mobile';
import { InformationCircleOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import Paragraph from 'antd/es/typography/Paragraph';
import LogsComponent from '../../device-logs/LogsComponent';

interface Props {
  name?: string;
  isMobile: boolean;
}

export default function MachineOverview(props: Props) {
  const { name: paramName } = useParams();
  const { name: propName, isMobile } = props;
  const name = propName || paramName;

  const sizeDeviceErrorRef = useRef(20);
  const sizeDeviceMainConfigRef = useRef(20);
  const machinesFacade = MachinesFacade();

  useEffect(() => {
    if (name) {
      machinesFacade.getMetadata({ name, params: {} });
      machinesFacade.getErrorList({ name, params: { size: sizeDeviceErrorRef.current } });
    }
  }, [name]);

  useEffect(() => {
    machinesFacade.data?.id &&
      machinesFacade.getDeviceMainConfig({
        deviceId: machinesFacade.data?.id,
        size: sizeDeviceMainConfigRef.current,
      });
  }, [machinesFacade.data]);

  const formatMinsAgo = (minsAgo?: number) => {
    if (minsAgo === undefined || minsAgo === null) return 'N/A';

    // If more than 24 hours (1440 minutes), show date in dd/mm format
    if (minsAgo > 1440) {
      const targetDate = dayjs().subtract(minsAgo, 'minute');
      return targetDate.format('DD/MM');
    }

    if (minsAgo < 60) return `${minsAgo}m`;
    const hours = Math.floor(minsAgo / 60);
    const mins = minsAgo % 60;
    return `${hours}h ${mins}m`;
  };

  const formatLastUpdate = (lastUpdate?: string) => {
    if (!lastUpdate) return 'N/A';
    return dayjs(lastUpdate).format('HH:mm');
  };

  const formatVersionInfo = (machine: any) => {
    const parts = [];

    if (machine.version) {
      parts.push(machine.version);
    }

    if (machine.backupVersion) {
      parts.push(machine.backupVersion);
    }

    if (machine.networkMode) {
      parts.push(machine.networkMode);
    }

    if (machine.hasMqtt) {
      parts.push('mq');
    }

    return parts.length > 0 ? parts.join('/') : 'N/A';
  };

  const truncateDeviceId = (deviceId: string, maxLength: number = 20): string => {
    if (deviceId.length <= maxLength) {
      return deviceId;
    }
    return deviceId.substring(0, maxLength) + '...';
  };

  const machine = machinesFacade.data;

  return (
    <div>
      {/* Machine Overview Card - Same format as machines list */}
      {machine && (
        <Card className="mb-2 h-auto text-xs" bodyStyle={{ padding: '0' }}>
          <Space className="gap-1.5 mb-3" direction="vertical" block>
            <Space wrap>
              <Tag
                color={
                  machinesFacade.data?.isCaptureThreadAlive === null
                    ? 'default'
                    : machinesFacade.data?.isCaptureThreadAlive
                      ? 'success'
                      : 'danger'
                }
                fill="outline"
              >
                Chụp <span>•</span>
              </Tag>
              <Tag
                color={
                  machinesFacade.data?.isUploadThreadAlive === null
                    ? 'default'
                    : machinesFacade.data?.isUploadThreadAlive
                      ? 'success'
                      : 'danger'
                }
                fill="outline"
              >
                Upload <span>•</span>
              </Tag>
              <Tag
                color={
                  machinesFacade.data?.isMaintainThreadAlive === null
                    ? 'default'
                    : machinesFacade.data?.isMaintainThreadAlive
                      ? 'success'
                      : 'danger'
                }
                fill="outline"
              >
                Bảo trì <span>•</span>
              </Tag>
              <Tag color={(machinesFacade.data?.batteryVolt || 0) < 11.8 ? 'danger' : 'primary'} fill="outline">
                Pin: {machinesFacade.data?.batteryVolt}V
              </Tag>
            </Space>
          </Space>
          <div className="flex flex-col h-full justify-between">
            <div className="flex flex-row justify-between items-center text-left">
              <Paragraph
                className="font-semibold text-sm"
                style={{
                  display: '-webkit-box',
                  overflow: 'hidden',
                  lineHeight: '1.2',
                }}
                copyable
              >
                {machine.deviceId}
              </Paragraph>
            </div>

            <div className="leading-tight mb-2">
              <div className="flex flex-row justify-between items-center mb-2">
                <div style={{ width: '70%' }}>
                  <Progress
                    percent={parseFloat(
                      (100 - (100 * (machine.freeSpace || 0)) / (machine.totalSpace || 1)).toFixed(2),
                    )}
                    strokeWidth={window.innerWidth < 768 ? 8 : 12}
                    status="active"
                    strokeColor={{ from: '#39d8ff', to: '#ff3966' }}
                    showInfo={false}
                  />
                </div>
                <div className="ml-2 font-semibold text-cyan-600">{machine.batteryVolt?.toFixed(2)}V</div>
              </div>

              {isMobile && (
                <>
                  <div className="flex flex-row justify-between items-center text-cyan-600 font-semibold mb-2">
                    <div>
                      {formatLastUpdate(machine.lastUpdate)} ({formatMinsAgo(machine.lastUpdatedMinsAgo)})
                    </div>
                    <div>
                      <div
                        className={
                          'font-semibold ' +
                          (machine.status === 1
                            ? 'text-green-600'
                            : machine.status === 0
                              ? 'text-red-600'
                              : 'text-orange-400')
                        }
                      >
                        {machine.status === 1
                          ? 'OK'
                          : machine.status === 0
                            ? 'OFF'
                            : 'SLEEP (' + dayjs(machine.wakeTime).format('HH:mm') + ')'}
                      </div>
                    </div>
                  </div>

                  <div
                    className={
                      'flex flex-row justify-between items-center font-semibold mb-2 ' +
                      (machine.lastCapturedMinsAgo != null && machine.lastCapturedMinsAgo <= 1440
                        ? 'text-cyan-600'
                        : 'text-red-600')
                    }
                  >
                    <div>
                      {formatLastUpdate(machine.lastCapturedTime)} ({formatMinsAgo(machine.lastCapturedMinsAgo)})
                    </div>
                    <div>{machine.lastCapturedImage?.split('.')[0]}</div>
                  </div>

                  <div
                    className={
                      'flex flex-row justify-between items-center font-semibold mb-2 ' +
                      (machine.lastUploadedMinsAgo != null && machine.lastUploadedMinsAgo <= 1440
                        ? 'text-cyan-600'
                        : 'text-red-600')
                    }
                  >
                    <div>
                      {formatLastUpdate(machine.lastUploadedTime)} ({formatMinsAgo(machine.lastUploadedMinsAgo)})
                    </div>
                    <div>{machine.lastUploadedImage?.split('.')[0]}</div>
                  </div>
                  <hr className="h-px my-2 bg-gray-200 border-0 "></hr>
                  <div className="flex flex-row justify-between items-center text-cyan-600 font-semibold">
                    <div>{formatVersionInfo(machine)}</div>
                    <div>{machine.uploadSpeed + 'KB/s'}</div>
                  </div>
                </>
              )}
            </div>
          </div>
        </Card>
      )}
      {!isMobile && (
        <div>
          <h2 className="text-lg font-semibold mb-2">Device logs</h2>
          <LogsComponent name={name} isMobile={isMobile} />
        </div>
      )}

      {/* Metadata Section */}
      <Collapse
        className="mb-4 [&_.adm-list-item-content-main]:text-[15px] "
        defaultActiveKey={!isMobile ? ['metadata'] : []}
      >
        <Collapse.Panel key="metadata" title="Metadata">
          <table className="table-fixed border border-collapse w-full text-sm">
            <tbody>
              {machinesFacade.metadata?.content?.map((metadata) => (
                <tr key={metadata.id} className="hover:bg-gray-50">
                  <td className="border border-gray-300 px-2 py-1 w-36 text-gray-600 break-words">{metadata.name}</td>
                  <td className="border border-gray-300 px-2 py-1 break-words text-black">{metadata.value}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </Collapse.Panel>
      </Collapse>

      {/* Error List Section */}
      <Collapse
        className="mb-4 [&_.adm-list-item-content-main]:text-[15px] "
        defaultActiveKey={!isMobile ? ['errorList'] : []}
      >
        <Collapse.Panel key="errorList" title="Danh sách lỗi">
          {machinesFacade.errorList?.content && machinesFacade.errorList?.content.length > 0 ? (
            <List
              className="text-black"
              style={{
                '--border-bottom': 'none',
                '--border-top': 'none',
              }}
            >
              <List.Item key={0}>
                <Grid columns={3}>
                  <Grid.Item className=" !text-[14px]" span={1}>
                    Mã lỗi
                  </Grid.Item>
                  <Grid.Item className=" !text-[14px]" span={2}>
                    Chi tiết
                  </Grid.Item>
                </Grid>
              </List.Item>

              {machinesFacade.errorList?.content.map((error) => (
                <SwipeAction
                  key={error.id}
                  rightActions={[
                    {
                      key: 'delete',
                      text: 'Xoá',
                      color: 'danger',
                      onClick: () => {
                        name &&
                          machinesFacade.deleteDeviceError({
                            name,
                            errorCode: error.errorCode,
                          });
                      },
                    },
                  ]}
                >
                  <List.Item key={error.id}>
                    <Grid columns={3}>
                      <Grid.Item className=" !text-[14px]" span={1}>
                        {error.errorCode}
                      </Grid.Item>
                      <Grid.Item className=" !text-[14px]" span={2}>
                        {error.errorDescription}
                      </Grid.Item>
                    </Grid>
                  </List.Item>
                </SwipeAction>
              ))}
            </List>
          ) : (
            <div style={{ textAlign: 'center', padding: '2rem', color: '#999' }}>Chưa có lỗi</div>
          )}
        </Collapse.Panel>
      </Collapse>

      {/* Camera Specs Section */}
      <Collapse
        className="mb-4 [&_.adm-list-item-content-main]:text-[15px]"
        defaultActiveKey={!isMobile ? ['cameraSpecs'] : []}
      >
        <Collapse.Panel key="cameraSpecs" title="Thông số máy ảnh">
          <Table
            showHeader={false}
            bordered
            size="small"
            tableLayout="fixed"
            rowKey={'id'}
            columns={[
              {
                dataIndex: 'name',
                key: 'name',
                width: 150,
                render: (value: string) => {
                  const pathToLabelMap: Record<string, string> = {
                    '/main/capturesettings/imagequality': 'Chất lượng ảnh (Image Quality)',
                    '/main/imgsettings/imagesize': 'Kích thước ảnh (Image Size)',
                    '/main/imgsettings/iso': 'Độ nhạy sáng (ISO)',
                    '/main/capturesettings/f-number': 'Khẩu độ (F-number)',
                    '/main/capturesettings/exposurecompensation': 'Bù sáng (EV-Exposure Value)',
                    '/main/capturesettings/expprogram': 'Chế độ phơi sáng (Exposure Program)',
                    '/main/imgsettings/whitebalance': 'Cân bằng trắng (White Balance)',
                    '/main/capturesettings/focallength': 'Tiêu cự (Focal Length)',
                  };

                  return <Typography.Text strong>{pathToLabelMap[value]}</Typography.Text>;
                },
              },
              {
                dataIndex: 'current',
                key: 'current',
                render: (value, record) => {
                  return (
                    <Space align="center">
                      <Typography.Text type="success" strong>
                        {value.label}
                      </Typography.Text>
                      <Popover.Menu
                        actions={record.choices?.map((choice) => ({
                          key: choice.value,
                          text: `${choice.value} - ${choice.label}`,
                        }))}
                        trigger="click"
                        maxCount={5}
                        placement="bottom-start"
                      >
                        <InformationCircleOutline fontSize={18} />
                      </Popover.Menu>
                    </Space>
                  );
                },
              },
              {
                dataIndex: 'lastUpdateCurrentValue',
                key: 'lastUpdateCurrentValue',
                width: 90,
                render: (value) => formatDayjsDate(value, 'HH:mm DD/MM/YYYY'),
              },
            ]}
            dataSource={machinesFacade.deviceMainConfig?.content}
            pagination={{
              total: machinesFacade.deviceMainConfig?.totalElements,
              current: machinesFacade.deviceMainConfig?.page,
              pageSize: sizeDeviceMainConfigRef.current,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showTotal: (total, range) => `Từ ${range[0]} đến ${range[1]} trên tổng ${total}`,
              onChange: (page, size) => {
                sizeDeviceMainConfigRef.current = size;
                machinesFacade.data?.id &&
                  machinesFacade.getDeviceMainConfig({ deviceId: machinesFacade.data?.id, page, size });
              },
            }}
          />
        </Collapse.Panel>
      </Collapse>
    </div>
  );
}
