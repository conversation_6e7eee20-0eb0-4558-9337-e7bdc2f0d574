import { MachinesFacade } from '@store';
import { formatDayjsDate, uuidv4 } from '@utils';
import { DotLoading, InfiniteScroll, PullToRefresh, Switch } from 'antd-mobile';
import { BackTop, Button, DatePicker, Input, Select } from 'antd';
import { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { RedoOutlined, RetweetOutlined } from '@ant-design/icons';

const { RangePicker } = DatePicker;

export default function FileLogsComponent({ name, isMobile }: { name?: string; isMobile: boolean }) {
  const machinesFacade = MachinesFacade();
  const sizeDeviceLogsRef = useRef(0);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [debouncedSearchKeyword, setDebouncedSearchKeyword] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [logType, setLogType] = useState<'Main' | 'Backup'>('Main');

  const fetchDeviceFileLogs = async (size?: number) => {
    if (!name) return;

    const params: {
      name: string;
      size?: number;
      keyword?: string;
      timeRanges?: string[];
      logType?: 'Main' | 'Backup';
    } = { name };

    if (size !== undefined) {
      params.size = size;
      sizeDeviceLogsRef.current = size;
    }

    if (debouncedSearchKeyword.trim()) {
      params.keyword = debouncedSearchKeyword.trim();
    }

    if (dateRange && dateRange[0] && dateRange[1]) {
      params.timeRanges = [dateRange[0].toISOString(), dateRange[1].toISOString()];
    }

    if (logType) {
      params.logType = logType;
    }

    if (size === undefined || size === 0) {
      setHasMore(true);
      sizeDeviceLogsRef.current = 0;
    }

    await machinesFacade.getDeviceFileLogs(params);
  };

  useEffect(() => {
    const currentLogsCount = machinesFacade.deviceFileLogs?.length || 0;

    if (sizeDeviceLogsRef.current > 0 && currentLogsCount < sizeDeviceLogsRef.current) {
      setHasMore(false);
    } else if (sizeDeviceLogsRef.current > 0 && currentLogsCount > 0) {
      setHasMore(true);
    }
  }, [machinesFacade.deviceFileLogs]);

  // Debounce effect for search keyword
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchKeyword(searchKeyword);
    }, 1000);

    return () => clearTimeout(timer);
  }, [searchKeyword]);

  useEffect(() => {
    fetchDeviceFileLogs(20);
  }, [name, debouncedSearchKeyword, dateRange, logType]);

  useEffect(() => {
    const handleScroll = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target && target.scrollTop !== undefined) {
        const scrollTop = target.scrollTop;
        const shouldShow = scrollTop > 300;
        setShowBackToTop(shouldShow);
      }
    };

    const scrollContainers = [document.querySelector('main')].filter(Boolean);

    scrollContainers.forEach((container) => {
      if (container) {
        container.addEventListener('scroll', handleScroll, { passive: true });
      }
    });
    return () => {
      scrollContainers.forEach((container) => {
        if (container) {
          container.removeEventListener('scroll', handleScroll);
        }
      });
    };
  }, []);

  const scrollToTop = () => {
    const scrollContainers = [document.querySelector('main')].filter(Boolean);

    scrollContainers.forEach((container) => {
      if (container && typeof container.scrollTo === 'function') {
        (container as any).scrollTo({ top: 0, behavior: 'smooth' });
      } else if (container) {
        (container as any).scrollTop = 0;
      }
    });
  };

  const formatLogContent = (content: string) => {
    if (!content) return '';
    const parts = content.split(' ');
    if (parts.length > 2) {
      const message = parts.slice(7).join(' ');
      if (message.length > 0 && /^[a-zA-Z]/.test(message)) {
        return message.charAt(0).toUpperCase() + message.slice(1);
      }
      return message;
    }
    return content;
  };

  const formatLogDate = (content: string) => {
    if (!content) return '';
    const parts = content.split(' ');
    if (parts.length > 1) {
      const dateTimeString = `${parts[0]} ${parts[1].replace(',', '.')}`;
      const d = dayjs(dateTimeString);
      if (d.isValid()) {
        return d.format('HH:mm:ss DD/MM');
      }
    }
    return '';
  };

  async function loadMoreDeviceLogs() {
    if (!hasMore) return;
    sizeDeviceLogsRef.current += 20;
    await fetchDeviceFileLogs(sizeDeviceLogsRef.current);
  }

  const handleRefresh = async () => {
    sizeDeviceLogsRef.current = 0;
    setHasMore(true);
    await fetchDeviceFileLogs();
  };

  const tableContent = (
    <table className="table-fixed w-full text-sm">
      <tbody>
        {machinesFacade.deviceFileLogs?.map((log, index) => (
          <tr key={uuidv4()} className={`${index % 2 === 0 ? 'bg-blue-50' : 'bg-white'} hover:bg-gray-50`}>
            <td className="px-2 py-0.5 w-27 text-gray-600 break-words">{formatLogDate(log.content)}</td>
            <td className="px-1 py-0.5 break-words">{formatLogContent(log.content)}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );

  return (
    <>
      <div className="px-4 pt-2 pb-2 bg-white ">
        {isMobile ? (
          <>
            <div className=" flex flex-row mb-2">
              <Input.Search
                placeholder="Tìm kiếm logs..."
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                className="w-full"
                allowClear
              />
              <Switch
                className="ml-2"
                checkedText={<div className="text-xs px-1 font-semibold">Main</div>}
                uncheckedText={<div className="text-xs px-1 font-semibold">Backup</div>}
                defaultChecked
                onChange={(checked: boolean) => {
                  checked ? setLogType('Main') : setLogType('Backup');
                }}
              />
            </div>
            <div className="mb-2">
              <RangePicker
                placeholder={['Từ ngày', 'Đến ngày']}
                value={dateRange}
                onChange={(dates) => setDateRange(dates)}
                showTime={{ format: 'HH:mm:ss' }}
                format="DD/MM/YYYY HH:mm:ss"
                className="w-full"
                allowClear
              />
            </div>
          </>
        ) : (
          <div className="flex flex-row items-center gap-2">
            <Input.Search
              placeholder="Tìm kiếm logs..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              allowClear
            />
            <RangePicker
              placeholder={['Từ ngày', 'Đến ngày']}
              value={dateRange}
              onChange={(dates) => setDateRange(dates)}
              showTime={{ format: 'HH:mm:ss' }}
              format="DD/MM/YYYY HH:mm:ss"
              allowClear
            />
            <Switch
              className="ml-2"
              checkedText={<div className="text-xs px-1 font-semibold">Main</div>}
              uncheckedText={<div className="text-xs px-1 font-semibold">Backup</div>}
              defaultChecked
              onChange={(checked: boolean) => {
                checked ? setLogType('Main') : setLogType('Backup');
              }}
            />
          </div>
        )}
      </div>

      <PullToRefresh
        canReleaseText="Thả để làm mới"
        completeText="Đã làm mới"
        pullingText="Kéo xuống để làm mới"
        refreshingText={
          <>
            <DotLoading /> Đang làm mới...
          </>
        }
        onRefresh={handleRefresh}
      >
        {tableContent}
      </PullToRefresh>

      <InfiniteScroll loadMore={loadMoreDeviceLogs} hasMore={hasMore}>
        {hasMore ? (
          <div className="text-center text-xs text-gray-400 py-2">
            <DotLoading /> Đang tải thêm...
          </div>
        ) : (
          <div className="text-center text-xs text-gray-400 py-2">Không còn dữ liệu</div>
        )}
      </InfiniteScroll>

      {showBackToTop && (
        <div
          className="fixed bottom-14 right-5 z-50 bg-gray-400 text-white rounded-full p-3 shadow-lg cursor-pointer hover:bg-blue-700 transition-all duration-300"
          onClick={scrollToTop}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
        </div>
      )}
    </>
  );
}
