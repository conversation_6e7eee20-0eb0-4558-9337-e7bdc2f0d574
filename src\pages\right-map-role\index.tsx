import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { SubHeader } from '@layouts/admin';
import { EStatusState } from '@models';
import UserGroupEditForm from '@pages/user/user-group-management/edit.drawer';
import { RolesFacade, RolesModel, UserFacade } from '@store';
import { App, Button, Card, Col, Form, Menu, MenuProps, Row, Space, Tooltip } from 'antd';
import { useEffect, useRef } from 'react';
import { EStatusRightMapRole, RightMapRole, RightMapRoleFacade } from 'src/store/right-map-role';
import DataDecentralizationTable from './dataDecentralization.table';
import RightMapEditableTable from './table';

const RightScreen = () => {
  const [form] = Form.useForm();
  const roleFacade = RolesFacade();
  const userFacade = UserFacade();
  const rightMapFacade = RightMapRoleFacade();
  const menuSelectedRef = useRef<string>();
  const { modal } = App.useApp();

  useEffect(() => {
    roleFacade.get({});
    userFacade.get({});
    rightMapFacade.getRightMapByListCode('ROLE,RIGHTMAPROLE');
  }, []);

  useEffect(() => {
    switch (roleFacade.status) {
      case EStatusState.getFulfilled:
        rightMapFacade.getConfig(menuSelectedRef.current ?? roleFacade.pagination?.content[0]?.id ?? '');
        break;
      case EStatusState.postFulfilled:
      case EStatusState.putFulfilled:
      case EStatusState.deleteFulfilled:
        roleFacade.get({});
        break;
    }
  }, [roleFacade.status]);

  useEffect(() => {
    switch (rightMapFacade.status) {
      case EStatusRightMapRole.getConfigFulfilled:
        form.setFieldValue('data', rightConfig);
        break;
    }
  }, [rightMapFacade.status]);

  const rightConfig = rightMapFacade.configList ?? [];

  const handleClickMenuItems = (key: string) => {
    if (menuSelectedRef.current !== key) {
      menuSelectedRef.current = key;
      rightMapFacade.getConfig(key);
    }
  };

  const handleEdit = (data: RolesModel) => {
    roleFacade.set({ data: data, isVisible: true, isEdit: true });
  };

  const handleRemove = (record: RolesModel) => {
    modal.confirm({
      title: 'Xác nhận xoá nhóm người dùng',
      content: `Nhóm người dùng ${record.code} sẽ bị xóa. Bạn có muốn tiếp tục?`,
      onOk: () => {
        record.id && roleFacade.delete(record.id);
      },
      onCancel: () => {},
      okText: 'Đồng ý',
      okButtonProps: { variant: 'outlined' },
      cancelText: 'Huỷ',
      cancelButtonProps: { danger: true },
    });
  };

  const roleSelectItems: MenuProps['items'] =
    roleFacade.pagination?.content.map((items, index) => ({
      label: (
        <div className={'flex justify-between w-full'}>
          <div className={'flex-1 truncate'}>
            {index + 1}. {items.name ?? ''}
          </div>
          <div>
            <Tooltip title={'Chỉnh sửa'}>
              <Button
                hidden={!rightMapFacade.rightDatas?.find((x) => x.groupCode === 'ROLE')?.isUpdateAllowed}
                type={'link'}
                className={'text-blue-500'}
                icon={<EditOutlined />}
                onClick={() => handleEdit(items)}
              />
            </Tooltip>
            <Tooltip title={'Xóa'}>
              <Button
                hidden={!rightMapFacade.rightDatas?.find((x) => x.groupCode === 'ROLE')?.isDeleteAllowed}
                color="danger"
                variant="link"
                icon={<DeleteOutlined />}
                onClick={() => handleRemove(items)}
              />
            </Tooltip>
          </div>
        </div>
      ),
      key: items.id ?? '',
    })) ?? [];

  const onFinish = (value: any) => {
    const data: RightMapRole[] = value.data;

    rightMapFacade.putConfig(menuSelectedRef.current ?? roleFacade.pagination?.content[0]?.id ?? '', data);
  };

  return (
    <>
      <SubHeader
        tool={
          <Button onClick={form.submit} htmlType="submit" type="primary">
            Lưu lại
          </Button>
        }
      />
      <UserGroupEditForm />
      <div className="max-w-7xl mx-auto p-6">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={8} xxl={6}>
            <Card
              className="-intro-x"
              loading={roleFacade.isLoading}
              title={'Nhóm người dùng'}
              extra={
                <Tooltip title={'Thêm mới nhóm người dùng'}>
                  <Button
                    hidden={!rightMapFacade.rightDatas?.find((x) => x.groupCode === 'ROLE')?.isCreateAllowed}
                    className={'mr-3'}
                    type={'primary'}
                    icon={<PlusOutlined />}
                    size="small"
                    shape={'circle'}
                    onClick={() => roleFacade.set({ isCreate: true })}
                  />
                </Tooltip>
              }
              size="small"
              variant="borderless"
            >
              <Menu
                className={'h-[calc(100vh-300px)] overflow-auto miniScroll'}
                inlineIndent={20}
                forceSubMenuRender={true}
                mode={'inline'}
                items={roleSelectItems}
                onClick={({ key }) => handleClickMenuItems(key)}
                selectedKeys={[menuSelectedRef.current ?? roleFacade.pagination?.content[0]?.id ?? '']}
              />
            </Card>
          </Col>
          <Col xs={24} sm={24} md={16} xxl={18}>
            <Card className="intro-x" loading={rightMapFacade.isLoading} size="small" variant="borderless">
              <Form form={form} onFinish={onFinish}>
                <Form.List name="data">
                  {(data, { add, remove }) => {
                    return (
                      <Space direction="vertical" size={'large'}>
                        <RightMapEditableTable data={data} form={form} disabledAdminId={menuSelectedRef.current} />
                        <DataDecentralizationTable data={data} form={form} disabledAdminId={menuSelectedRef.current} />
                      </Space>
                    );
                  }}
                </Form.List>
              </Form>
            </Card>
          </Col>
        </Row>
      </div>
    </>
  );
};

export default RightScreen;
