import { InfoCircleOutlined, KeyOutlined, LockOutlined, SaveOutlined, UndoOutlined } from '@ant-design/icons';
import { Button, Card, Col, DatePicker, Flex, Form, Input, Row, Select, Typography } from 'antd';
import { useEffect } from 'react';
import { useNavigate } from 'react-router';

import { SubHeader } from '@layouts/admin';
import { AddressFacade, ChangePasswordModel, EStatusGlobal, GlobalFacade, User, UserFacade } from '@store';
import { lang, routerLinks } from '@utils';
import dayjs from 'dayjs';
import { useSearchParams } from 'react-router-dom';

export const tabs = {
  INFO_ACCOUNT: 'INFO_ACCOUNT',
  CHANGE_PASSWORD: 'CHANGE_PASSWORD',
};

const Page = () => {
  const { user, isLoading, status, putProfile, data, changePasswordProfile } = GlobalFacade();
  const userFacade = UserFacade();
  const addressFacade = AddressFacade();
  const [accountForm] = Form.useForm();
  const [changePasswordForm] = Form.useForm();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const tab = searchParams.get('tab');


  useEffect(() => {
    switch (status) {
      case EStatusGlobal.putProfileFulfilled:
        break;
      case EStatusGlobal.changePasswordProfileFulfilled:
        changePasswordForm.resetFields();
        break;
      case EStatusGlobal.profileFulfilled:
        if (tab === tabs.INFO_ACCOUNT) {
          for (const key in data?.userModel) {
            if (key === 'birthdate') {
              accountForm.setFieldValue(key, data?.userModel[key] ? dayjs(data?.userModel[key]) : '');
            } else {
              accountForm.setFieldValue(key, data?.userModel[key as keyof User]);
            }
          }
        }
        break;
    }
  }, [status]);

  const onPutFinish = (values: User) => {
    putProfile({
      id: data?.userId,
      ...values,
    });
  };

  const onChangePasswordFinish = (values: User) => {
    if (data?.userId) {
      changePasswordProfile({
        id: data?.userId,
        values,
      });
    }
  };

  return (
    <>
      <SubHeader
        tool={
          <>
            <Button
              size="large"
              type="text"
              className={`${tab === tabs.INFO_ACCOUNT && '!bg-blue-50'}`}
              onClick={() => navigate(`${routerLinks('MyProfile')}?tab=${tabs.INFO_ACCOUNT}`)}
            >
              <Flex vertical justify="center" align="center">
                <InfoCircleOutlined />
                <Typography.Text>Thông tin tài khoản</Typography.Text>
              </Flex>
            </Button>
            <Button
              className={`${tab === tabs.CHANGE_PASSWORD && '!bg-blue-50'}`}
              size="large"
              type="text"
              onClick={() => navigate(`${routerLinks('MyProfile')}?tab=${tabs.CHANGE_PASSWORD}`)}
            >
              <Flex vertical justify="center" align="center">
                <KeyOutlined />
                <Typography.Text>Đổi mật khẩu</Typography.Text>
              </Flex>
            </Button>
          </>
        }
      />
      <div className="m-6">
        <Card loading={isLoading}>
          {tab === tabs.INFO_ACCOUNT ? (
            <Form className="intro-x" layout="vertical" autoComplete="off" form={accountForm} onFinish={onPutFinish}>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item<User>
                    name="name"
                    label="Tên tài khoản"
                    rules={[{ required: true, message: 'Tên tài khoản là bắt buộc!' }]}
                  >
                    <Input placeholder="Nhập tên tài khoản" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item<User>
                    name="email"
                    label="Email"
                    rules={[{ type: 'email', message: 'Email không hợp lệ!' }]}
                  >
                    <Input placeholder="Nhập email" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item<User>
                    name="phoneNumber"
                    label="Số điện thoại"
                    rules={[
                      { required: true, message: 'Số điện thoại là bắt buộc!' },
                      {
                        pattern: /^[0-9]{9,10}$/,
                        message: 'Số điện thoại không đúng định dạng',
                      },
                    ]}
                  >
                    <Input placeholder="Nhập số điện thoại" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item<User> name="birthdate" label="Ngày sinh">
                    <DatePicker className="w-full" format={'DD/MM/YYYY'} placeholder="Chọn ngày sinh" />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item<User> name="address" label="Địa chỉ chi tiết">
                    <Input.TextArea rows={1} placeholder="Nhập địa chỉ chi tiết" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item<User> name="provinceCode" label="Tỉnh/TP">
                    <Select
                      placeholder="Chọn Tỉnh/TP"
                      allowClear
                      showSearch
                      optionFilterProp="label"
                      options={addressFacade.listTinh?.map((item) => ({
                        value: item?.value,
                        label: item.label,
                      }))}
                      onClick={() => {
                        addressFacade.getTinh();
                      }}
                      onChange={(value: number) => {
                        addressFacade.getHuyen({ filter: JSON.stringify({ parentId: value }) });
                        accountForm.resetFields(['districtCode', 'wardCode']);
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item<User> name="districtCode" label="Quận/Huyện">
                    <Select
                      placeholder="Chọn Quận/Huyện"
                      allowClear
                      showSearch
                      optionFilterProp="label"
                      options={addressFacade.listHuyen?.map((item) => ({
                        value: item?.value,
                        label: item.label,
                      }))}
                      onClick={(value) => {
                        addressFacade.getHuyen({ filter: JSON.stringify({ parentId: value }) });
                      }}
                      onChange={(value: number) => {
                        addressFacade.getXa({ filter: JSON.stringify({ parentId: value }) });
                        accountForm.resetFields(['wardCode']);
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item<User> name="wardCode" label="Phường/Xã">
                    <Select
                      placeholder="Chọn Phường/Xã"
                      allowClear
                      showSearch
                      optionFilterProp="label"
                      options={addressFacade.listXa?.map((item) => ({
                        value: item.value,
                        label: item.label,
                      }))}
                    />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Flex justify="end" align="center" gap={10}>
                    <Button
                      icon={<SaveOutlined />}
                      color="primary"
                      variant="solid"
                      htmlType="submit"
                      onClick={() => accountForm.submit()}
                    >
                      Lưu thông tin
                    </Button>
                    <Button
                      icon={<UndoOutlined />}
                      color="danger"
                      variant="outlined"
                      onClick={() => accountForm.resetFields()}
                    >
                      Nhập lại
                    </Button>
                  </Flex>
                </Col>
              </Row>
            </Form>
          ) : (
            <Form
              className="intro-x"
              layout="vertical"
              autoComplete="off"
              form={changePasswordForm}
              onFinish={onChangePasswordFinish}
            >
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item<ChangePasswordModel>
                    label="Mật khẩu mới"
                    name="password"
                    rules={[
                      {
                        required: true,
                        message: 'Mật khẩu là bắt buộc!',
                      },
                      {
                        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/,
                        message: 'Mật khẩu cần có ít nhất 8 ký tự, gồm chữ hoa, chữ thường, số và ký tự đặc biệt!',
                      },
                    ]}
                    hasFeedback
                  >
                    <Input.Password prefix={<LockOutlined />} placeholder={'Mật khẩu mới'} />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item<ChangePasswordModel>
                    label="Nhập lại mật khẩu mới"
                    name="confirmPassword"
                    dependencies={['password']}
                    rules={[
                      {
                        required: true,
                        message: 'Mật khẩu mới là bắt buộc!',
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('password') === value) {
                            return Promise.resolve();
                          }
                          return Promise.reject(new Error('Mật khẩu mới không khớp!'));
                        },
                      }),
                    ]}
                    hasFeedback
                  >
                    <Input.Password prefix={<LockOutlined />} placeholder={'Nhập lại mật khẩu mới'} />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Flex justify="end" align="center" gap={10}>
                    <Button
                      icon={<SaveOutlined />}
                      color="primary"
                      variant="solid"
                      htmlType="submit"
                      onClick={() => changePasswordForm.submit()}
                    >
                      Lưu thông tin
                    </Button>
                    <Button
                      icon={<UndoOutlined />}
                      color="danger"
                      variant="outlined"
                      onClick={() => changePasswordForm.resetFields()}
                    >
                      Nhập lại
                    </Button>
                  </Flex>
                </Col>
              </Row>
            </Form>
          )}
        </Card>
      </div>
    </>
  );
};
export default Page;
