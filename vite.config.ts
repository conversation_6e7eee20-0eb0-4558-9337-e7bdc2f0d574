import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import tsconfigPaths from 'vite-tsconfig-paths';
import svgr from 'vite-plugin-svgr';
import tailwindcss from '@tailwindcss/vite';
import { VitePWA } from 'vite-plugin-pwa';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    host: '0.0.0.0',
    port: 4000,
    watch: {
      usePolling: true,
    },
  },
  plugins: [
    react(),
    tailwindcss(),
    tsconfigPaths(),
    svgr({
      include: '**/*.svg?react',
    }),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}']
      },
      includeAssets: ['favicon.ico', 'logo192.png', 'logo512.png'],
      manifest: {
        name: 'Monitor PWA',
        short_name: 'Monitor',
        description: 'Monitor PWA Application',
        theme_color: '#2563eb',
        background_color: '#eff6ff',
        display: 'standalone',
        scope: './',
        start_url: './?utm_source=pwa',
        orientation: 'portrait',
        icons: [
          {
            src: 'logo192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'logo512.png',
            sizes: '512x512',
            type: 'image/png'
          },
          {
            src: 'maskable_icon_x512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'maskable'
          }
        ]
      },
      devOptions: {
        enabled: true
      }
    })
  ],
  build: {
    outDir: './build',
    rollupOptions: {
      output: {
        minifyInternalExports: true,
        entryFileNames: `assets/[name].js`,
        chunkFileNames: `assets/[name].js`,
        assetFileNames: `assets/[name].[ext]`,
      },
    },
  },
  resolve: {
    alias: [
      { find: /^~/, replacement: '' },
      { find: '@svgs', replacement: path.resolve(__dirname, 'src/assets/svgs') },
    ],
  },
});
