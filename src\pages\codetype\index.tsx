import { DeleteOutlined, EditOutlined, PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import { SubHeader } from '@layouts/admin';
import { EStatusState, QueryParams } from '@models';
import { InputSearch } from '@pages/shared-directory/search-widget';
import {
  CodeTypeManagement,
  CodeTypeManagementFacade,
  TypesCodeTypeManagement,
  TypesCodeTypeManagementFacade,
} from '@store';
import { formatDayjsDate, scrollLeftWhenChanging, scrollTopWhenChanging } from '@utils';
import {
  Button,
  Card,
  Col,
  Flex,
  FormInstance,
  Menu,
  MenuProps,
  Modal,
  Row,
  Space,
  Table,
  Tooltip,
  Typography,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useEffect, useRef } from 'react';
import { useNavigate } from 'react-router';
import { useSearchParams } from 'react-router-dom';
import EditForm from './edit.drawer';

interface DataType extends CodeTypeManagement {
  stt?: number;
  key?: string;
}

const Page: React.FC = () => {
  const navigate = useNavigate();
  const menuRef = useRef<string>('PAYMENT_METHOD');
  const [modalApi, contextModalApi] = Modal.useModal();
  const [searchParams, setSearchParams] = useSearchParams();
  const formRef = useRef<FormInstance | undefined>(undefined);
  const codeTypeManagementFacade = CodeTypeManagementFacade();
  const typesCodeTypeManagementFacade = TypesCodeTypeManagementFacade();
  const {
    page,
    size,
    filter = '{}',
    sort = '',
    id = '',
  } = {
    ...Object.fromEntries(searchParams),
    page: Number(searchParams.get('page') || 1),
    size: Number(searchParams.get('size') || 20),
  };
  const parsedFilter = JSON.parse(filter);
  // nếu không có type thì mặc định là PAYMENT_METHOD
  if (!parsedFilter.type) {
    parsedFilter.type = 'PAYMENT_METHOD';
  }

  useEffect(() => {
    onChangeDataTable({ query: { page, size, filter: JSON.stringify({ ...parsedFilter }) } });
    typesCodeTypeManagementFacade.get({ size: -1 });
  }, []);

  useEffect(() => {
    switch (codeTypeManagementFacade.status) {
      case EStatusState.postFulfilled:
      case EStatusState.putFulfilled:
      case EStatusState.deleteFulfilled:
        onChangeDataTable({});
        break;
    }
  }, [codeTypeManagementFacade.status]);

  const dataSource: DataType[] =
    codeTypeManagementFacade.pagination?.content.map(
      (items, index): DataType => ({
        stt:
          (Number(codeTypeManagementFacade.pagination?.page ?? 0) - 1) *
            Number(codeTypeManagementFacade.pagination?.size ?? 0) +
          index +
          1,
        ...items,
      }),
    ) ?? [];

  const onChangeSearch = (value: string) => {
    if (value) {
      parsedFilter.fullTextSearch = value;
    } else {
      delete parsedFilter.fullTextSearch;
    }
    onChangeDataTable({
      query: {
        page: 1,
        size,
        filter: JSON.stringify({ ...parsedFilter }),
      },
    });
  };

  const handleDelete = (id: string) => {
    modalApi.confirm({
      title: 'Xác nhận xoá dữ liệu tronyg danh mục',
      content: 'Dữ liệu trong danh mục sẽ bị xóa. Bạn có muốn tiếp tục?',
      onOk: () => {
        codeTypeManagementFacade.delete(id);
      },
      onCancel: () => {},
      okText: 'Đồng ý',
      okButtonProps: { variant: 'outlined' },
      cancelText: 'Huỷ',
      cancelButtonProps: { danger: true },
    });
  };

  const onChangeDataTable = (props: { query?: QueryParams; setKeyState?: object }) => {
    if (!props.query) {
      props.query = {
        page,
        size,
        filter,
        sort,
        id,
      };
    }
    const fillQuery: QueryParams = { ...codeTypeManagementFacade.query, ...props.query };
    for (const key in fillQuery) {
      if (!fillQuery[key as keyof QueryParams]) delete fillQuery[key as keyof QueryParams];
    }
    codeTypeManagementFacade.get(fillQuery);
    navigate(
      { search: new URLSearchParams(fillQuery as unknown as Record<string, string>).toString() },
      { replace: true },
    );
    codeTypeManagementFacade.set({ query: props.query, ...props.setKeyState });
  };

  const column: ColumnsType<DataType> = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      align: 'center',
      width: 50,
      fixed: 'left',
    },
    {
      title: 'Mã',
      dataIndex: 'code',
      key: 'code',
      width: 150,
    },
    {
      title: 'Tên',
      dataIndex: 'title',
      key: 'title',
      width: 150,
      render: (value, record) => (
        <Space>
          <i className={`la-lg ${record?.iconClass}`}></i>
          <Typography.Text>{value}</Typography.Text>
        </Space>
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdOnDate',
      key: 'createdOnDate',
      width: 150,
      render: (value) => formatDayjsDate(value),
    },
    {
      title: 'Thao tác',
      dataIndex: 'action',
      key: 'Action',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: (_, record: CodeTypeManagement) => (
        <Space>
          <Tooltip title="Chỉnh sửa">
            <Button
              size="small"
              color="blue"
              variant="outlined"
              icon={<EditOutlined />}
              onClick={() => {
                if (record.id) {
                  codeTypeManagementFacade.set({
                    isVisibleForm: true,
                  });
                  codeTypeManagementFacade.getById({ id: record.id });
                  setSearchParams((prev) => {
                    prev.set('id', record.id ?? '');
                    return prev;
                  });
                }
              }}
            />
          </Tooltip>
          <Tooltip title="Xoá">
            <Button
              size="small"
              color="danger"
              variant="outlined"
              icon={<DeleteOutlined />}
              onClick={() => {
                if (record.id) {
                  handleDelete(record.id);
                }
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const menuItems: MenuProps['items'] =
    typesCodeTypeManagementFacade.pagination?.content.map((item: TypesCodeTypeManagement) => ({
      label: item.title,
      key: item.code,
    })) ?? [];

  const handleClickMenuItems = (key: string) => {
    menuRef.current = key;
    formRef.current?.resetFields();
    parsedFilter.type = key;
    onChangeDataTable({
      query: {
        page: 1,
        size,
        filter: JSON.stringify({ ...parsedFilter }),
      },
    });
    scrollLeftWhenChanging('.ant-table-body');
    scrollTopWhenChanging('.ant-table-body');
  };

  return (
    <>
      <SubHeader />
      {contextModalApi}
      <div className="container mx-auto p-6">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={8} lg={6} xl={6}>
            <Card
              className="-intro-x"
              title={'Danh mục'}
              loading={typesCodeTypeManagementFacade.isLoading}
              size="small"
              variant="borderless"
            >
              <Menu
                inlineIndent={12}
                defaultSelectedKeys={[parsedFilter?.type]}
                forceSubMenuRender={true}
                mode={'inline'}
                items={menuItems}
                onClick={({ key }) => handleClickMenuItems(key)}
              />
            </Card>
          </Col>
          <Col xs={24} sm={24} md={16} lg={18} xl={18}>
            <Card className="intro-x" loading={codeTypeManagementFacade.isLoading} size="small" variant="borderless">
              <Flex className="!mb-4 flex-col gap-3 sm:flex-row sm:items-center" wrap gap={10}>
                <div className="w-full sm:w-auto flex-1">
                  <InputSearch
                    defaultValue={parsedFilter?.fullTextSearch}
                    callback={onChangeSearch}
                    placeholder="Tìm kiếm theo mã, tên"
                  />
                </div>

                <div className="flex flex-wrap items-center gap-3 sm:gap-2">
                  <Button
                    icon={<ReloadOutlined />}
                    loading={codeTypeManagementFacade.isLoading}
                    onClick={() =>
                      onChangeDataTable({
                        query: {
                          page: 1,
                          size,
                          filter: JSON.stringify({ ...parsedFilter }),
                        },
                      })
                    }
                  >
                    Tải lại
                  </Button>
                  <Button
                    type={'primary'}
                    icon={<PlusOutlined />}
                    onClick={() =>
                      codeTypeManagementFacade.set({
                        isVisibleForm: true,
                      })
                    }
                  >
                    Thêm mới
                  </Button>
                </div>
              </Flex>
              <Table
                size="small"
                scroll={{ x: 'max-content', y: 'calc(100vh - 220px)' }}
                dataSource={dataSource}
                columns={column}
                pagination={{
                  size: 'small',
                  className: 'pr-4',
                  showSizeChanger: true,
                  current: codeTypeManagementFacade?.pagination?.page,
                  pageSize: codeTypeManagementFacade?.pagination?.size,
                  total: codeTypeManagementFacade?.pagination?.totalElements,
                  pageSizeOptions: [20, 40, 60, 80],
                  showTotal: (total, range) => `Từ ${range[0]} đến ${range[1]} trên tổng ${total}`,
                  onChange: (page, size) => {
                    let query = codeTypeManagementFacade.query;
                    query = { ...query, page: page, size: size };
                    onChangeDataTable({ query: query });
                  },
                }}
                rowKey={'id'}
                // expandable={{
                //   expandedRowRender: (record) => (
                //     <Table
                //       showHeader={false}
                //       size="small"
                //       pagination={false}
                //       dataSource={record.codeTypeItems}
                //       columns={[
                //         {
                //           title: 'STT',
                //           dataIndex: 'lineNumber',
                //           key: 'lineNumber',
                //           align: 'center',
                //           width: 80,
                //           render: (_, record) => {
                //             const parentIndex = dataSource.findIndex((item) => item.id === record.codeTypeId);
                //             const parentStt = dataSource[parentIndex]?.stt ?? 0;
                //             return `${parentStt}.${record.lineNumber}`;
                //           },
                //         },
                //         {
                //           title: 'Mã',
                //           dataIndex: 'code',
                //           key: 'code',
                //           width: 150,
                //         },
                //         {
                //           title: 'Tên',
                //           dataIndex: 'title',
                //           key: 'title',
                //           render: (value, record) => (
                //             <Space>
                //               <i className={`la-lg ${record?.iconClass}`}></i>
                //               <Typography.Text>{value}</Typography.Text>
                //             </Space>
                //           ),
                //         },
                //         {
                //           title: 'Ngày tạo',
                //           dataIndex: 'createdOnDate',
                //           key: 'createdOnDate',
                //           width: 150,
                //           render: (value) => formatDayjsDate(value),
                //         },
                //         {
                //           title: null,
                //           dataIndex: 'action',
                //           key: 'Action',
                //           align: 'center',
                //           width: 150,
                //         },
                //       ]}
                //     />
                //   ),
                //   rowExpandable: (record: any) => record.codeTypeItems && record.codeTypeItems.length > 0,
                // }}
              />
            </Card>
          </Col>
        </Row>
      </div>
      <EditForm />
    </>
  );
};

export default Page;
