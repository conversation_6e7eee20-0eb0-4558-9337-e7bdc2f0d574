/**
 * GPS Component Type Definitions
 * 
 * This file contains all TypeScript type definitions used by the GPS component
 * and related functionality.
 */

// Environment configuration interface
export interface Environment {
  /** API URL for admin services */
  adminApiUrl: string;
  /** Whether the app is running in production mode */
  production: boolean;
  /** User data encryption key */
  userData: string;
  /** GPS tracking service URL */
  gpsUrl: string;
}

// Scene configuration (matching Angular component structure)
export interface Scene {
  /** Container type */
  type: 'container';
  /** Container properties */
  props: {
    /** Layout orientation */
    orientation: 'horizontal' | 'vertical';
  };
  /** Child elements */
  children: SceneChild[];
}

// Scene child element
export interface SceneChild {
  /** Unique identifier */
  id?: string | number;
  /** Element type */
  type: string;
  /** Element data */
  data?: any;
  /** Nested children */
  children?: SceneChild[];
}

// GPS Component props interface
export interface GpsComponentProps {
  /** Environment configuration */
  environment?: Environment;
  /** Additional CSS class names */
  className?: string;
  /** Callback fired when GPS iframe loads successfully */
  onLoad?: () => void;
  /** Callback fired when GPS iframe fails to load */
  onError?: (error: Error) => void;
  /** Custom iframe title */
  title?: string;
  /** Whether to allow fullscreen */
  allowFullScreen?: boolean;
  /** Loading strategy for iframe */
  loading?: 'lazy' | 'eager';
}

// GPS Demo component props
export interface GpsDemoProps {
  /** Initial environment configuration */
  initialEnvironment?: Environment;
  /** Whether to show controls panel */
  showControls?: boolean;
  /** Whether to show status indicators */
  showStatus?: boolean;
  /** Custom header title */
  headerTitle?: string;
  /** Custom header subtitle */
  headerSubtitle?: string;
}

// GPS loading states
export type GpsLoadingState = 'idle' | 'loading' | 'loaded' | 'error';

// GPS error types
export interface GpsError {
  /** Error message */
  message: string;
  /** Error code */
  code?: string;
  /** Timestamp when error occurred */
  timestamp: Date;
  /** Additional error details */
  details?: Record<string, any>;
}

// GPS status information
export interface GpsStatus {
  /** Current loading state */
  state: GpsLoadingState;
  /** Whether GPS is currently loading */
  isLoading: boolean;
  /** Whether there's an error */
  hasError: boolean;
  /** Error information if any */
  error?: GpsError;
  /** Last successful load timestamp */
  lastLoaded?: Date;
  /** Current GPS URL */
  currentUrl?: string;
}

// Environment utility functions type
export interface EnvironmentUtils {
  /** Get current environment */
  getEnvironment: () => Environment;
  /** Check if running in production */
  isProduction: () => boolean;
  /** Get API URL */
  getApiUrl: () => string;
  /** Get GPS URL */
  getGpsUrl: () => string;
  /** Switch environment */
  setEnvironment: (env: Environment) => void;
}

// GPS component ref interface
export interface GpsComponentRef {
  /** Reload the GPS iframe */
  reload: () => void;
  /** Get current status */
  getStatus: () => GpsStatus;
  /** Update GPS URL */
  updateUrl: (url: string) => void;
  /** Get iframe element */
  getIframe: () => HTMLIFrameElement | null;
}

// Layout management interface
export interface LayoutManager {
  /** Add layout classes */
  addLayoutClasses: () => void;
  /** Remove layout classes */
  removeLayoutClasses: () => void;
  /** Toggle layout classes */
  toggleLayoutClasses: (add: boolean) => void;
}

// GPS configuration options
export interface GpsConfig {
  /** Default GPS URL */
  defaultUrl: string;
  /** Timeout for loading in milliseconds */
  loadTimeout?: number;
  /** Whether to retry on error */
  retryOnError?: boolean;
  /** Number of retry attempts */
  maxRetries?: number;
  /** Retry delay in milliseconds */
  retryDelay?: number;
  /** Custom error messages */
  errorMessages?: Record<string, string>;
}

// Event handlers interface
export interface GpsEventHandlers {
  /** Handle load event */
  onLoad?: (event: Event) => void;
  /** Handle error event */
  onError?: (error: Error) => void;
  /** Handle before load event */
  onBeforeLoad?: () => void;
  /** Handle timeout event */
  onTimeout?: () => void;
  /** Handle retry event */
  onRetry?: (attempt: number) => void;
}

// GPS metrics interface
export interface GpsMetrics {
  /** Load time in milliseconds */
  loadTime?: number;
  /** Number of load attempts */
  loadAttempts: number;
  /** Number of errors */
  errorCount: number;
  /** Last error timestamp */
  lastError?: Date;
  /** Total uptime */
  uptime?: number;
}
