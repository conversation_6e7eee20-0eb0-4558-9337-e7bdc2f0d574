import { Navigation, NavigationFacade } from '@store';
import { lang } from '@utils';
import { Breadcrumb, Flex, TreeNodeProps } from 'antd';
import { ReactNode, useEffect } from 'react';
import { useLocation } from 'react-router';

type _T_Props = {
  breadCrumb?: ReactNode;
  tool?: ReactNode;
  onReload?: () => void;
  visible?: boolean;
};
let parentMenu: TreeNodeProps[];
let subMenus: Navigation;
export const AppBreadCrumb = (props: _T_Props) => {
  const navigationFacade = NavigationFacade();
  const location = useLocation();

  useEffect(() => {
    if (navigationFacade && navigationFacade.menu) {
      subMenus = navigationFacade.menu
        .flatMap((itemFlatMap) => {
          if (itemFlatMap.subChild.length > 0) return [itemFlatMap, ...itemFlatMap.subChild];
          return itemFlatMap;
        })
        .find((itemFilter) => `${itemFilter.urlRewrite}` === location.pathname);
      parentMenu = navigationFacade.menu.filter((item) => item.id === subMenus?.parentId);
    }
  }, [location.pathname]);
  subMenus = navigationFacade.menu
    ?.flatMap((itemFlatMap) => {
      if (itemFlatMap.subChild.length > 0) return [itemFlatMap, ...itemFlatMap.subChild];
      return itemFlatMap;
    })
    .find((itemFilter) => `${itemFilter.urlRewrite}` === location.pathname);
  parentMenu = navigationFacade.menu?.filter((item) => item.id === subMenus?.parentId) ?? [];

  return (
    <Flex>
      {subMenus && subMenus?.parentId ? (
        <Breadcrumb
          items={[
            {
              title: parentMenu[0]?.name,
            },
            {
              title: subMenus.name,
            },
          ]}
        />
      ) : (
        subMenus?.name && navigationFacade.menu && navigationFacade.menu?.length > 0 && subMenus?.name
      )}
      {
        <Breadcrumb
          items={[
            {
              title: props.breadCrumb,
            },
          ]}
        />
      }
    </Flex>
  );
};
