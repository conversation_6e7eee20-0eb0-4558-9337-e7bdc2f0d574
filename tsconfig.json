{"extends": "./tsconfig.paths.json", "compilerOptions": {"target": "ES2022", "module": "ES2022", "useDefineForClassFields": true, "lib": ["dom", "DOM.Iterable", "ES2022"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "types": ["node", "vite-plugin-svgr/client"], "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "importHelpers": true, "outDir": "./dist/out-tsc"}, "include": ["./src/**/*"], "watchOptions": {"watchFile": "fixedPollingInterval"}}