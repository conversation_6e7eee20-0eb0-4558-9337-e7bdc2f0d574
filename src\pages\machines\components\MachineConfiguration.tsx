import { MachinesFacade, MachineConfigModel, EStatusMachine } from '@store';
import { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router';
import { Input, Button, List, Typography, Space, Spin, message, Modal } from 'antd';
import { SearchOutlined, EditOutlined, SaveOutlined, SyncOutlined, SettingOutlined } from '@ant-design/icons';
import { DotLoading, PullToRefresh } from 'antd-mobile';

const { Search } = Input;
const { Text } = Typography;

interface Props {
  name?: string;
}

export default function MachineConfiguration(props: Props) {
  const { name: paramName } = useParams();
  const { name: propName } = props;
  const name = propName || paramName; // Use prop name if provided, otherwise use URL param

  const machinesFacade = MachinesFacade();
  const [searchTerm, setSearchTerm] = useState('');
  const [editingConfig, setEditingConfig] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);

  const [originalConfigs, setOriginalConfigs] = useState<MachineConfigModel[]>([]);
  const [editedConfigs, setEditedConfigs] = useState<MachineConfigModel[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [pushingConfigId, setPushingConfigId] = useState<string | null>(null);

  const { machineConfig, isLoading, status } = machinesFacade;

  useEffect(() => {
    if (name) {
      machinesFacade.getMachineConfig(name);
      machinesFacade.getByName({ name });
    }
  }, [name]);

  useEffect(() => {
    if (machineConfig && machineConfig.length > 0) {
      setOriginalConfigs([...machineConfig]);
      setEditedConfigs([...machineConfig]);
      setHasChanges(false);
    }
  }, [machineConfig]);

  const filteredConfigs = useMemo(
    () =>
      editedConfigs?.filter(
        (config) =>
          config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          config.value.toLowerCase().includes(searchTerm.toLowerCase()),
      ) || [],
    [editedConfigs, searchTerm],
  );

  const handleEditClick = (config: MachineConfigModel) => {
    setEditingConfig(config.id);
    setEditValue(config.value);
  };

  const handleSaveClick = (config: MachineConfigModel) => {
    // Update the edited configs with the new value
    const updatedConfigs = editedConfigs.map((c) =>
      c.id === config.id ? { ...c, value: editValue, lastModifiedOnDate: new Date().toISOString() } : c,
    );
    setEditedConfigs(updatedConfigs);
    setHasChanges(true);

    console.log('Saving config:', config.name, 'with value:', editValue);
    message.success(`Configuration ${config.name} updated successfully`);
    setEditingConfig(null);
    setEditValue('');
  };

  const handleCancelEdit = () => {
    setEditingConfig(null);
    setEditValue('');
  };

  const handlePushSingle = async (config: MachineConfigModel) => {
    if (!name) {
      message.error('Device name is required');
      return;
    }

    if (!machinesFacade.data?.name) {
      message.error('Device not found');
      return;
    }

    setPushingConfigId(config.id);

    try {
      await machinesFacade.postDeviceCommand({
        deviceId: machinesFacade.data.name,
        values: {
          name: `config:${config.name}`,
          value: editValue,
          type: 'PUSH_CMD',
          service: 'MAIN',
        },
      });

      // Update the edited configs with the new value after successful push
      const updatedConfigs = editedConfigs.map((c) =>
        c.id === config.id ? { ...c, value: editValue, lastModifiedOnDate: new Date().toISOString() } : c,
      );
      setEditedConfigs(updatedConfigs);
      setHasChanges(true);

      setEditingConfig(null);
      setEditValue('');
      setPushingConfigId(null);
      message.success(`Configuration ${config.name} pushed to device successfully`);
      machinesFacade.getMachineConfig(name);
    } catch (error) {
      setPushingConfigId(null);
      message.error('Failed to push configuration to device');
      console.error('Push single error:', error);
    }
  };

  // const handleSaveAll = () => {
  //   // Save all current edits (this just confirms the current edited state)
  //   setHasChanges(true);
  //   message.success('All configurations saved locally. Use "Push all" to send to device.');
  // };

  const handlePushAll = async () => {
    if (!name) {
      message.error('Device name is required');
      return;
    }

    if (!hasChanges) {
      message.info('No changes to push');
      return;
    }

    try {
      await machinesFacade.changeConfig({
        deviceName: name,
        configs: editedConfigs,
      });

      // Update original configs to match edited configs after successful push
      setOriginalConfigs([...editedConfigs]);
      setHasChanges(false);
      message.success('All configurations pushed to device successfully');
      machinesFacade.getMachineConfig(name);
    } catch (error) {
      message.error('Failed to push configurations to device');
      console.error('Push error:', error);
    }
  };

  // const handleResetMachine = () => {
  //   setIsModalVisible(true);
  // };

  const handleResetConfirm = () => {
    // Reset to original configurations
    setEditedConfigs([...originalConfigs]);
    setHasChanges(false);
    setEditingConfig(null);
    setEditValue('');
    setIsModalVisible(false);
    message.success('Configurations reset to original values');
  };

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <PullToRefresh
      canReleaseText="Thả để làm mới"
      completeText="Đã làm mới"
      pullingText="Kéo xuống để làm mới"
      refreshingText={
        <>
          <DotLoading /> Đang làm mới...
        </>
      }
      onRefresh={async () => {
        name && machinesFacade.getMachineConfig(name);
      }}
    >
      <div className="space-y-4 px-2">
        {/* Search Bar - Sticky */}
        <div className="sticky top-20 z-10 bg-white pb-4 border-b mb-0 border-gray-100">
          <div className="flex gap-2">
          <Search
            placeholder="Tìm kiếm cấu hình..."
            allowClear
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            prefix={<SearchOutlined />}
            style={{ width: '100%' }}
          />
          <Button
            type={hasChanges ? 'primary' : 'default'}
            icon={<SyncOutlined />}
            onClick={handlePushAll}
            loading={status === EStatusMachine.changeConfigPending}
            disabled={!hasChanges}
          >
            Push all {hasChanges && '(*)'}
          </Button>
          </div>
        </div>

        <div>
          {filteredConfigs.map((config) => (
            <div key={config.id} className="py-3 space-y-1 border-b border-gray-100">
              <div className="font-medium break-all">{config.name}</div>
              <div className="flex gap-2 items-center">
                {editingConfig === config.id ? (
                  <>
                    <Input.TextArea
                      id={`input-${config.id}`}
                      value={editValue}
                      onChange={(e) => setEditValue(e.target.value)}
                      onPressEnter={() => handleSaveClick(config)}
                      className="flex-1"
                      autoSize={{ minRows: 1 }}
                    />
                    <div className="flex gap-2">
                      <Button type="primary" onClick={() => handleSaveClick(config)}>
                        Save
                      </Button>
                      <Button onClick={() => handlePushSingle(config)} loading={pushingConfigId === config.id}>
                        Push
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    <p className="text-sm text-gray-500 flex-1 text-wrap break-all">{config.value}</p>
                    <Button
                      key="edit"
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() => {
                        handleEditClick(config);
                        setTimeout(() => {
                          document.getElementById(`input-${config.id}`)?.focus();
                        }, 0);
                      }}
                      style={{ color: '#1890ff' }}
                    />
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
        {/* {status === EStatusMachine.getMachineConfigRejected ? (
        <div style={{ textAlign: 'center', padding: '2rem', color: '#ff4d4f' }}>
          <p>Lỗi khi tải cấu hình. Vui lòng thử lại.</p>
          <Button onClick={() => name && machinesFacade.getMachineConfig(name)}>Thử lại</Button>
        </div>
      ) : filteredConfigs.length > 0 ? (
        <List
          dataSource={filteredConfigs}
          renderItem={(config) => {
            const originalConfig = originalConfigs.find((oc) => oc.id === config.id);
            const isModified = originalConfig && originalConfig.value !== config.value;

            return (
              <List.Item
                actions={[
                  editingConfig === config.id ? (
                    <Space key="edit-actions">
                      <Button
                        type="primary"
                        size="small"
                        icon={<SaveOutlined />}
                        onClick={() => handleSaveClick(config)}
                      >
                        Save
                      </Button>
                      <Button
                        size="small"
                        icon={<SyncOutlined />}
                        onClick={() => handlePushSingle(config)}
                        loading={pushingConfigId === config.id}
                      >
                        Push
                      </Button>
                      <Button size="small" onClick={handleCancelEdit}>
                        Cancel
                      </Button>
                    </Space>
                  ) : (
                    <Button
                      key="edit"
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() => handleEditClick(config)}
                      style={{ color: '#1890ff' }}
                    />
                  ),
                ]}
              >
                <List.Item.Meta
                  title={
                    <Text strong style={{ fontSize: '0.9rem' }}>
                      {config.name}: {isModified && <span style={{ color: '#52c41a' }}>*</span>}
                    </Text>
                  }
                  description={
                    editingConfig === config.id ? (
                      <Input
                        value={editValue}
                        onChange={(e) => setEditValue(e.target.value)}
                        onPressEnter={() => handleSaveClick(config)}
                        style={{ marginTop: '0.25rem' }}
                      />
                    ) : (
                      <Text style={{ fontSize: '0.9rem', color: '#666' }}>{config.value}</Text>
                    )
                  }
                />
              </List.Item>
            );
          }}
        />
      ) : status === EStatusMachine.getMachineConfigFulfilled ? (
        <div style={{ textAlign: 'center', padding: '2rem', color: '#999' }}>
          {searchTerm ? 'Không tìm thấy cấu hình phù hợp' : 'Không có cấu hình nào'}
        </div>
      ) : (
        <div style={{ textAlign: 'center', padding: '2rem', color: '#999' }}>Đang tải cấu hình...</div>
      )} */}

        <Modal
          title="Xác nhận reset máy"
          open={isModalVisible}
          onOk={handleResetConfirm}
          onCancel={() => setIsModalVisible(false)}
          okText="Xác nhận"
          cancelText="Hủy"
          okButtonProps={{ danger: true }}
        >
          <p>Bạn có chắc chắn muốn reset máy này không? Hành động này không thể hoàn tác.</p>
        </Modal>
      </div>
    </PullToRefresh>
  );
}
