import { MachinesFacade } from '@store';
import { DotLoading, PullToRefresh } from 'antd-mobile';

interface Props {
  name?: string;
}
export default function MachineMetadataList(props: Props) {
  const { name } = props;
  const machinesFacade = MachinesFacade();
  return (
    <>
      <PullToRefresh
        canReleaseText="Thả để làm mới"
        completeText="Đã làm mới"
        pullingText="Kéo xuống để làm mới"
        refreshingText={
          <>
            <DotLoading /> Đang làm mới...
          </>
        }
        onRefresh={async () => {
          name && machinesFacade.getMetadata({ name, params: {} });
        }}
      >
        <table className="table-fixed border border-collapse w-full text-sm">
          <tbody>
            {machinesFacade.metadata?.content?.map((metadata) => (
              <tr key={metadata.id} className="hover:bg-gray-50">
                <td className="border border-gray-300 px-2 py-1 w-36 text-gray-600 break-words">{metadata.name}</td>
                <td className="border border-gray-300 px-2 py-1 break-words">{metadata.value}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </PullToRefresh>
    </>
  );
}
