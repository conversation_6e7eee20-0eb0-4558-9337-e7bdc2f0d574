//.ant-table {
//  &-content {
//    .ant-table-thead > tr > th {
//      @apply font-bold bg-white border-none border-0;
//      &.ant-table-cell-fix-left,
//      &.ant-table-cell-fix-right {
//        @apply ~'mt-[1px]';
//      }
//    }
//    .ant-table-tbody > tr.ant-table-row {
//      &:nth-of-type(odd) > td {
//        // @apply border-b bg-gray-100;
//        &:first-child {
//          @apply rounded-bl-xl rounded-tl-xl;
//        }
//        &:last-child {
//          @apply rounded-br-xl rounded-tr-xl;
//        }
//      }
//      > td {
//        // @apply border-none bg-white;
//        &:first-child {
//          @apply rounded-bl-xl rounded-tl-xl;
//        }
//        &:last-child {
//          @apply rounded-br-xl rounded-tr-xl;
//        }
//      }
//    }
//  }
//  &-filter-dropdown {
//    .ant-checkbox-group {
//      @apply inline-block;
//    }
//    .ant-checkbox-group-item,
//    .ant-radio-wrapper {
//      @apply flex ml-1;
//    }
//    .ant-radio-input {
//      @apply ~'mt-1 mr-1';
//    }
//  }
//  &-content .ant-table-thead > tr {
//    @apply relative;
//    .bg {
//      @apply absolute top-0 left-0 w-full h-full z-50;
//    }
//
//    > th {
//      @apply relative !bg-gray-100;
//    }
//  }
//  &-column-sorter-inner > .anticon {
//    @apply !mt-0;
//    &:not(.active) {
//      @apply hidden;
//    }
//  }
//}
