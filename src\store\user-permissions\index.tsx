import { Draft, createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { Message } from '@core/message';
import { CommonEntity, EStatusState, Pagination, QueryParams, Responses } from '@models';
import { Action, Slice, State, useAppDispatch, useTypedSelector } from '@store';
import { UserModel } from '@store/user';
import { API } from '@utils';
import { message } from 'antd';

const name = 'UserPermissions';
const action = {
  ...new Action<UserModel, EStatusUserPermissions>(name),
  getUsers: createAsyncThunk(name + '/getUsers', async (params: QueryParams) => {
    const res = await API.get<Pagination<UserModel>>('https://api-monitor.autotimelapse.com/api/users', params);
    return res;
  }),
  getUserDevices: createAsyncThunk(name + '/getUserDevices', async (params: { userId: string } & QueryParams) => {
    const { userId, ...queryParams } = params;
    const res = await API.get<Pagination<UserDeviceModel>>(
      `https://api-monitor.autotimelapse.com/api/users/${userId}/device`,
      queryParams
    );
    return res;
  }),
  addReadPermission: createAsyncThunk(
    name + '/addReadPermission',
    async ({ userId, deviceId }: { userId: string; deviceId: string }) => {
      const { message } = await API.post(
        `https://api-monitor.autotimelapse.com/api/users/${userId}/device/${deviceId}/permissions/r`,
        {}
      );
      if (message) Message.success({ content: message });
      return message;
    }
  ),
  removeReadPermission: createAsyncThunk(
    name + '/removeReadPermission',
    async ({ userId, deviceId }: { userId: string; deviceId: string }) => {
      const { message } = await API.delete(
        `https://api-monitor.autotimelapse.com/api/users/${userId}/device/${deviceId}/permissions/r`
      );
      if (message) Message.success({ content: message });
      return message;
    }
  ),
  addWritePermission: createAsyncThunk(
    name + '/addWritePermission',
    async ({ userId, deviceId }: { userId: string; deviceId: string }) => {
      const { message } = await API.post(
        `https://api-monitor.autotimelapse.com/api/users/${userId}/device/${deviceId}/permissions/w`,
        {}
      );
      if (message) Message.success({ content: message });
      return message;
    }
  ),
  removeWritePermission: createAsyncThunk(
    name + '/removeWritePermission',
    async ({ userId, deviceId }: { userId: string; deviceId: string }) => {
      const { message } = await API.delete(
        `https://api-monitor.autotimelapse.com/api/users/${userId}/device/${deviceId}/permissions/w`
      );
      if (message) Message.success({ content: message });
      return message;
    }
  ),
};

export const userPermissionsSlice = createSlice({
  ...new Slice<UserModel, EStatusUserPermissions>(
    action,
    {
      keepUnusedDataFor: 9999,
    },
    (builder) => {
      builder
        .addCase(action.getUsers.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusUserPermissions.getUsersPending;
        })
        .addCase(action.getUsers.fulfilled, (state, action) => {
          if (action.payload) {
            state.users = action.payload.data as Draft<Pagination<UserModel>>;
            state.status = EStatusUserPermissions.getUsersFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getUsers.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusUserPermissions.getUsersRejected;
        })
        .addCase(action.getUserDevices.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusUserPermissions.getUserDevicesPending;
        })
        .addCase(action.getUserDevices.fulfilled, (state, action) => {
          if (action.payload) {
            state.userDevices = action.payload.data as Draft<Pagination<UserDeviceModel>>;
            state.status = EStatusUserPermissions.getUserDevicesFulfilled;
          } else state.status = EStatusState.idle;
          state.isLoading = false;
        })
        .addCase(action.getUserDevices.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusUserPermissions.getUserDevicesRejected;
        })
        .addCase(action.addReadPermission.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusUserPermissions.addReadPermissionPending;
        })
        .addCase(action.addReadPermission.fulfilled, (state, action) => {
          state.isLoading = false;
          state.status = EStatusUserPermissions.addReadPermissionFulfilled;
        })
        .addCase(action.addReadPermission.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusUserPermissions.addReadPermissionRejected;
        })
        .addCase(action.removeReadPermission.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusUserPermissions.removeReadPermissionPending;
        })
        .addCase(action.removeReadPermission.fulfilled, (state, action) => {
          state.isLoading = false;
          state.status = EStatusUserPermissions.removeReadPermissionFulfilled;
        })
        .addCase(action.removeReadPermission.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusUserPermissions.removeReadPermissionRejected;
        })
        .addCase(action.addWritePermission.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusUserPermissions.addWritePermissionPending;
        })
        .addCase(action.addWritePermission.fulfilled, (state, action) => {
          state.isLoading = false;
          state.status = EStatusUserPermissions.addWritePermissionFulfilled;
        })
        .addCase(action.addWritePermission.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusUserPermissions.addWritePermissionRejected;
        })
        .addCase(action.removeWritePermission.pending, (state) => {
          state.isLoading = true;
          state.status = EStatusUserPermissions.removeWritePermissionPending;
        })
        .addCase(action.removeWritePermission.fulfilled, (state, action) => {
          state.isLoading = false;
          state.status = EStatusUserPermissions.removeWritePermissionFulfilled;
        })
        .addCase(action.removeWritePermission.rejected, (state) => {
          state.isLoading = false;
          state.status = EStatusUserPermissions.removeWritePermissionRejected;
        });
    }
  ),
});

export const UserPermissionsFacade = () => {
  const dispatch = useAppDispatch();
  return {
    ...useTypedSelector((state) => state[action.name] as StateUserPermissions<UserModel>),
    set: (values: StateUserPermissions<UserModel>) => dispatch(action.set(values)),
    get: (params: QueryParams) => dispatch(action.get(params)),
    getUsers: (params: QueryParams) => dispatch(action.getUsers(params)),
    getUserDevices: (params: { userId: string } & QueryParams) => dispatch(action.getUserDevices(params)),
    getById: ({ id, keyState = 'isVisible' }: { id: string; keyState?: keyof StateUserPermissions<UserModel> }) =>
      dispatch(action.getById({ id, keyState })),
    post: (values: UserModel) => dispatch(action.post({ values })),
    put: (values: UserModel) => dispatch(action.put({ values })),
    putDisable: (values: { id: string; disable: boolean }) => dispatch(action.putDisable(values)),
    delete: (values: { id: string }) => dispatch(action.delete(values)),
    addReadPermission: (params: { userId: string; deviceId: string }) => dispatch(action.addReadPermission(params)),
    removeReadPermission: (params: { userId: string; deviceId: string }) => dispatch(action.removeReadPermission(params)),
    addWritePermission: (params: { userId: string; deviceId: string }) => dispatch(action.addWritePermission(params)),
    removeWritePermission: (params: { userId: string; deviceId: string }) => dispatch(action.removeWritePermission(params)),
  };
};

interface StateUserPermissions<T> extends State<T, EStatusUserPermissions> {
  users?: Pagination<UserModel>;
  userDevices?: Pagination<UserDeviceModel>;
}

export class UserDeviceModel extends CommonEntity {
  constructor(
    public id: string,
    public userId?: string,
    public deviceId?: string,
    public deviceName?: string,
    public hasReadPermission?: boolean,
    public hasWritePermission?: boolean,
    public assignedDate?: string,
  ) {
    super();
  }
}

export enum EStatusUserPermissions {
  getUsersPending = 'getUsersPending',
  getUsersFulfilled = 'getUsersFulfilled',
  getUsersRejected = 'getUsersRejected',

  getUserDevicesPending = 'getUserDevicesPending',
  getUserDevicesFulfilled = 'getUserDevicesFulfilled',
  getUserDevicesRejected = 'getUserDevicesRejected',

  addReadPermissionPending = 'addReadPermissionPending',
  addReadPermissionFulfilled = 'addReadPermissionFulfilled',
  addReadPermissionRejected = 'addReadPermissionRejected',

  removeReadPermissionPending = 'removeReadPermissionPending',
  removeReadPermissionFulfilled = 'removeReadPermissionFulfilled',
  removeReadPermissionRejected = 'removeReadPermissionRejected',

  addWritePermissionPending = 'addWritePermissionPending',
  addWritePermissionFulfilled = 'addWritePermissionFulfilled',
  addWritePermissionRejected = 'addWritePermissionRejected',

  removeWritePermissionPending = 'removeWritePermissionPending',
  removeWritePermissionFulfilled = 'removeWritePermissionFulfilled',
  removeWritePermissionRejected = 'removeWritePermissionRejected',
}
