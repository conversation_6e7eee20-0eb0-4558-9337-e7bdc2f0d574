import { Checkbox, Form, Input, Table, Typography } from 'antd';
import { RightMapRoleFacade } from 'src/store/right-map-role';

const { Column } = Table;

const RightMapEditableTable = (props: any) => {
  const { data, disabledAdminId } = props;
  const rightMapRoleFacade = RightMapRoleFacade();
  return (
    <>
      <Table
        title={() => (
          <Typography.Title className="!mb-0" level={5}>
            Phân quyền chức năng
          </Typography.Title>
        )}
        size={'small'}
        dataSource={data}
        pagination={false}
        bordered
        rowClassName={(record) => (record.name === 4 ? 'hidden' : '')}
      >
        <Column
          align={'left'}
          dataIndex={'groupName'}
          title={'Chức năng'}
          fixed={'left'}
          render={(value, row: any, index) => {
            return (
              <Form.Item noStyle name={[index, 'groupName']}>
                <Input readOnly variant="borderless" />
              </Form.Item>
            );
          }}
        />
        <Column
          width={100}
          align={'center'}
          dataIndex={'isViewAllowed'}
          title={'Xem'}
          render={(value, row: any, index) => {
            return (
              <Form.Item noStyle name={[index, 'isViewAllowed']} valuePropName="checked">
                <Checkbox
                  disabled={
                    disabledAdminId === '00000000-0000-0000-0000-000000000001' ||
                    !rightMapRoleFacade.rightDatas?.find((x) => x.groupCode === 'RIGHTMAPROLE')?.isUpdateAllowed
                  }
                />
              </Form.Item>
            );
          }}
        />
        <Column
          width={100}
          align={'center'}
          dataIndex={'isViewAllAllowed'}
          title={'Xem tất cả'}
          render={(value, row: any, index) => {
            return (
              <Form.Item noStyle name={[index, 'isViewAllAllowed']} valuePropName="checked">
                <Checkbox
                  disabled={
                    disabledAdminId === '00000000-0000-0000-0000-000000000001' ||
                    !rightMapRoleFacade.rightDatas?.find((x) => x.groupCode === 'RIGHTMAPROLE')?.isUpdateAllowed
                  }
                />
              </Form.Item>
            );
          }}
        />
        <Column
          width={100}
          align={'center'}
          dataIndex={'isCreateAllowed'}
          title={'Thêm'}
          render={(value, row: any, index) => {
            return (
              <Form.Item noStyle name={[index, 'isCreateAllowed']} valuePropName="checked">
                <Checkbox
                  disabled={
                    disabledAdminId === '00000000-0000-0000-0000-000000000001' ||
                    !rightMapRoleFacade.rightDatas?.find((x) => x.groupCode === 'RIGHTMAPROLE')?.isUpdateAllowed
                  }
                />
              </Form.Item>
            );
          }}
        />
        <Column
          width={100}
          align={'center'}
          dataIndex={'isUpdateAllowed'}
          title={'Cập nhật'}
          render={(value, row, index) => {
            return (
              <Form.Item noStyle name={[index, 'isUpdateAllowed']} valuePropName="checked">
                <Checkbox
                  disabled={
                    disabledAdminId === '00000000-0000-0000-0000-000000000001' ||
                    !rightMapRoleFacade.rightDatas?.find((x) => x.groupCode === 'RIGHTMAPROLE')?.isUpdateAllowed
                  }
                />
              </Form.Item>
            );
          }}
        />
        <Column
          width={100}
          align={'center'}
          dataIndex={'isDeleteAllowed'}
          title={'Xóa'}
          render={(value, row, index) => {
            return (
              <Form.Item noStyle name={[index, 'isDeleteAllowed']} valuePropName="checked">
                <Checkbox
                  disabled={
                    disabledAdminId === '00000000-0000-0000-0000-000000000001' ||
                    !rightMapRoleFacade.rightDatas?.find((x) => x.groupCode === 'RIGHTMAPROLE')?.isUpdateAllowed
                  }
                />
              </Form.Item>
            );
          }}
        />
      </Table>
    </>
  );
};

export default RightMapEditableTable;
