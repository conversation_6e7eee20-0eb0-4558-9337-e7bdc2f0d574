import { Checkbox, Form, Table, Typography } from 'antd';
import { RightMapRoleFacade } from 'src/store/right-map-role';

const { Column } = Table;

const DataDecentralizationTable = (props: any) => {
  const { data, disabledAdminId } = props;
  const rightMapRoleFacade = RightMapRoleFacade();
  return (
    <>
      <Table
        title={() => (
          <Typography.Title className="!mb-0" level={5}>
            Phân quyền dữ liệu
          </Typography.Title>
        )}
        size={'small'}
        scroll={{ y: 'calc(100vh - 220px)' }}
        dataSource={data}
        pagination={false}
        bordered
        rowClassName={(record) => (record.name !== 4 ? 'hidden' : '')}
      >
        <Column
          align={'center'}
          dataIndex={'isShowColumnRecipientCollection'}
          title={'Tiền thu người nhận'}
          render={(value, row, index) => {
            return (
              <Form.Item noStyle name={[index, 'isShowColumnRecipientCollection']} valuePropName="checked">
                <Checkbox
                  disabled={
                    disabledAdminId === '00000000-0000-0000-0000-000000000001' ||
                    !rightMapRoleFacade.rightDatas?.find((x) => x.groupCode === 'RIGHTMAPROLE')?.isUpdateAllowed
                  }
                />
              </Form.Item>
            );
          }}
        />
        <Column
          align={'center'}
          dataIndex={'isShowColumnTransactionFleetCollection'}
          title={'Tiền thu nhà xe'}
          render={(value, row, index) => {
            return (
              <Form.Item noStyle name={[index, 'isShowColumnTransactionFleetCollection']} valuePropName="checked">
                <Checkbox
                  disabled={
                    disabledAdminId === '00000000-0000-0000-0000-000000000001' ||
                    !rightMapRoleFacade.rightDatas?.find((x) => x.groupCode === 'RIGHTMAPROLE')?.isUpdateAllowed
                  }
                />
              </Form.Item>
            );
          }}
        />
        <Column
          align={'center'}
          dataIndex={'isShowColumnSenderFleetCollection'}
          title={'Tiền thu người gửi'}
          render={(value, row, index) => {
            return (
              <Form.Item noStyle name={[index, 'isShowColumnSenderFleetCollection']} valuePropName="checked">
                <Checkbox
                  disabled={
                    disabledAdminId === '00000000-0000-0000-0000-000000000001' ||
                    !rightMapRoleFacade.rightDatas?.find((x) => x.groupCode === 'RIGHTMAPROLE')?.isUpdateAllowed
                  }
                />
              </Form.Item>
            );
          }}
        />
      </Table>
    </>
  );
};

export default DataDecentralizationTable;
