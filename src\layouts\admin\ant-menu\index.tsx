import { Navigation, NavigationFacade } from '@store';
import { lang, renderTitleBreadcrumbs } from '@utils';
import { Menu, theme, TreeNodeProps } from 'antd';
import { CameraOutline } from 'antd-mobile-icons';
import { MenuItemType } from 'antd/lib/menu/interface';
import { useEffect } from 'react';
import { useNavigate } from 'react-router';

const AntMenu = () => {
  const { useToken } = theme;
  const { token } = useToken();
  const navigationFacade = NavigationFacade();
  const navigate = useNavigate();
  const onChange = (key: string) => {
    const linkActive = `${key}`;
    navigate(linkActive);
  };

  navigationFacade?.menu?.forEach((item: any) => {
    if (item?.children?.length === 0 && location.hash.includes(lang + item?.urlRewrite))
      renderTitleBreadcrumbs(item.name);
    else if (item?.children?.some((subItem: any) => location.hash.includes(lang + subItem?.urlRewrite)))
      renderTitleBreadcrumbs(item.name);
  });

  useEffect(() => {}, [location]);

  const menuItems: MenuItemType[] = [
    {
      key: '/machines',
      icon: <CameraOutline />,
      label: 'Danh sách máy',
    },
  ];

  const defaultSubMenu: Navigation = navigationFacade.menu
    ?.flatMap((itemFlatMap) => {
      if (itemFlatMap.subChild.length > 0) return [itemFlatMap, ...itemFlatMap.subChild];
      return itemFlatMap;
    })
    .find((itemFilter) => location.hash.startsWith(`#${itemFilter.urlRewrite}`));

  const defaultMenu: TreeNodeProps[] =
    navigationFacade.menu?.filter((items) => items.id === defaultSubMenu?.parentId) ?? [];

  return (
    <>
      {defaultSubMenu?.urlRewrite && defaultSubMenu.urlRewrite.length > 0 ? (
        <Menu
          key={`9b2fe5aa-007f-478a-8f61-933e4056c356`}
          defaultOpenKeys={[defaultMenu[0]?.urlRewrite]}
          defaultSelectedKeys={[defaultSubMenu?.urlRewrite]}
          forceSubMenuRender
          className={`h-[calc(100vh-7rem)] font-medium overflow-auto miniScroll`}
          items={menuItems}
          mode={'inline'}
          onClick={({ key }) => onChange(key)}
          selectedKeys={[defaultSubMenu?.urlRewrite]}
        />
      ) : (
        <Menu
          key={`3414ce98-4cde-40c5-a54b-3dc81bdf3938`}
          defaultOpenKeys={[defaultMenu[0]?.urlRewrite]}
          defaultSelectedKeys={[defaultSubMenu?.urlRewrite]}
          forceSubMenuRender
          className={`h-[calc(100vh-7rem)] font-medium overflow-auto miniScroll`}
          items={menuItems}
          mode={'inline'}
          onClick={({ key }) => onChange(key)}
          selectedKeys={[defaultSubMenu?.urlRewrite]}
        />
      )}
    </>
  );
};
export default AntMenu;
