import { useEffect, useState } from 'react';
import { Input, Button, Tag, Typography, Space, Spin, Progress, Dropdown } from 'antd';
import { LogoutOutlined, ReloadOutlined, SearchOutlined, UserOutlined } from '@ant-design/icons';
import { MachinesFacade, MachineModel, GlobalFacade } from '@store';
import { checkAuthAndRedirect } from '@utils';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router';
import { DotLoading, NavBar, PullToRefresh } from 'antd-mobile';
import { useIsMobile } from 'src/utils/hooks/useIsMobile';

const { Search } = Input;
const { Text, Title } = Typography;

export default function AllMachines() {
  const globalFacade = GlobalFacade();
  const machinesFacade = MachinesFacade();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'live' | 'dead' | 'sleep' | 'problem'>('all');
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  useEffect(() => {
    // Check authentication
    if (!checkAuthAndRedirect()) {
      return;
    }

    // Load machines data
    machinesFacade.getMachines({
      time: 300,
      page: 1,
      size: -1,
      filter: '{}',
    });
  }, []);

  const handleRefresh = () => {
    machinesFacade.getMachines({
      time: 300,
      page: 1,
      size: -1,
      filter: '{}',
    });
  };

  const getStatusColor = (machine: MachineModel) => {
    // status = 2 (sleep) or status = 0 (off)
    if (machine.status === 2 || machine.status === 0) {
      let timeForComparison: string | undefined | null = machine.wakeTime;

      if (machine.lastUpdate) {
        if (timeForComparison) {
          if (dayjs(machine.lastUpdate).isAfter(dayjs(timeForComparison))) {
            timeForComparison = machine.lastUpdate;
          }
        } else {
          timeForComparison = machine.lastUpdate;
        }
      }

      if (timeForComparison) {
        const referenceTime = dayjs(timeForComparison);
        const now = dayjs();
        if (now.isAfter(referenceTime)) {
          const diffMinutes = now.diff(referenceTime, 'minute');
          if (diffMinutes > 1440) {
            // over 1 days
            return '!bg-slate-300';
          }
          if (diffMinutes > 5 && diffMinutes < 1440) {
            // over 5 mins and below 24 hours
            return '!bg-orange-200';
          }
        }
      }
    }

    // status = 1 && heart = 1 (online)
    if ((machine.status === 1 && machine.heartbeatStatus === 1)) {
      if (
        (machine.lastCapturedMinsAgo != null && machine.lastCapturedMinsAgo > 120) ||
        (machine.lastUploadedMinsAgo != null && machine.lastUploadedMinsAgo > 120)
      ) {
        return '!bg-purple-200';
      }
      return '';
    }

    // status = 1 && heart = 2 (in command)
    if (machine.status === 1 && machine.heartbeatStatus === 2) {
      return '!bg-lime-200';
    }

    // Default colors for states without special conditions
    if (machine.status === 0) {
      return '!bg-slate-300';
    }
    if (machine.status === 2) {
      if (
        (machine.lastCapturedMinsAgo != null && machine.lastCapturedMinsAgo > 120) ||
        (machine.lastUploadedMinsAgo != null && machine.lastUploadedMinsAgo > 120)
      ) {
        return '!bg-purple-200';
      }
      return '!bg-indigo-100';
    }

    return '!bg-indigo-100'; // A general fallback
  };

  const getStatusText = (status: number, heartbeatStatus: number) => {
    if (status === 1 && heartbeatStatus === 1) return 'LIVE';
    if (status === 0) return 'DEAD';
    return 'SLEEP';
  };

  const filteredMachines =
    machinesFacade.pagination?.content?.filter((machine: MachineModel) => {
      const matchesSearch =
        machine.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        machine.deviceId?.toLowerCase().includes(searchTerm.toLowerCase());

      if (statusFilter === 'all') return matchesSearch;

      if (statusFilter === 'problem') {
        const color = getStatusColor(machine);
        return matchesSearch && (color === '!bg-orange-200' || color === '!bg-purple-200');
      }

      const machineStatus = getStatusText(machine.status || 0, machine.heartbeatStatus || 0).toLowerCase();
      return matchesSearch && machineStatus === statusFilter;
    }) || [];

  const formatMinsAgo = (minsAgo?: number) => {
    if (minsAgo === undefined || minsAgo === null) return 'N/A';

    // If more than 24 hours (1440 minutes), show date in dd/mm format
    if (minsAgo > 1440) {
      const targetDate = dayjs().subtract(minsAgo, 'minute');
      return targetDate.format('DD/MM');
    }

    if (minsAgo < 60) return `${minsAgo}m`;
    const hours = Math.floor(minsAgo / 60);
    const mins = minsAgo % 60;
    return `${hours}h ${mins}m`;
  };

  const formatLastUpdate = (lastUpdate?: string) => {
    if (!lastUpdate) return 'N/A';
    return dayjs(lastUpdate).format('HH:mm');
  };

  const formatVersionInfo = (machine: MachineModel) => {
    const parts = [];

    if (machine.version) {
      parts.push(machine.version);
    }

    if (machine.backupVersion) {
      parts.push(machine.backupVersion);
    }

    if (machine.networkMode) {
      parts.push(machine.networkMode);
    }

    if (machine.hasMqtt) {
      parts.push('mq');
    }

    return parts.length > 0 ? parts.join('/') : 'N/A';
  };

  if (machinesFacade.isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <>
      <div className="p-4 space-y-4 mt-16 lg:max-w-[1500px] lg:mx-auto">
        <div>
          <div className="flex flex-wrap gap-2 items-center mb-4">
            <Space.Compact>
              <Button type={statusFilter === 'all' ? 'primary' : 'default'} onClick={() => setStatusFilter('all')}>
                All
              </Button>
              <Button type={statusFilter === 'live' ? 'primary' : 'default'} onClick={() => setStatusFilter('live')}>
                Live
              </Button>
              <Button type={statusFilter === 'dead' ? 'primary' : 'default'} onClick={() => setStatusFilter('dead')}>
                Dead
              </Button>
              <Button type={statusFilter === 'sleep' ? 'primary' : 'default'} onClick={() => setStatusFilter('sleep')}>
                Sleep
              </Button>
              <Button
                type={statusFilter === 'problem' ? 'primary' : 'default'}
                onClick={() => setStatusFilter('problem')}
              >
                Problem
              </Button>
            </Space.Compact>

            <Search
              placeholder="Nhập để tìm kiếm..."
              allowClear
              style={{ width: '18.75rem', minWidth: '15rem' }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              prefix={<SearchOutlined />}
            />

            {!isMobile && (
              <Button
                type="primary"
                onClick={async () => {
                  handleRefresh();
                }}
              >
                <ReloadOutlined /> Reload
              </Button>
            )}
          </div>
        </div>

        <PullToRefresh
          canReleaseText="Thả để làm mới"
          completeText="Đã làm mới"
          pullingText="Kéo xuống để làm mới"
          refreshingText={
            <>
              <DotLoading /> Đang làm mới...
            </>
          }
          onRefresh={async () => {
            handleRefresh();
          }}
        >
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-2">
            {filteredMachines.map((machine: MachineModel, index: number) => (
              <div
                className={
                  (getStatusColor(machine) || 'border border-gray-200') +
                  ' shadow-md text-xs sm:text-xs md:text-[13px] p-2 pb-0 relative flex flex-col justify-between rounded-sm cursor-pointer'
                }
                key={machine.id}
                onClick={() => navigate(`/machine/${machine.deviceId || machine.name || machine.id}/configuration`)}
              >
                <div className="flex flex-row justify-between w-full ">
                  <div className="hover:underline items-center block font-bold text-gray-700 no-underline break-all !text-sm max-[450px]:!text-xs">
                    {index + 1 + '. ' + machine.deviceId}{' '}
                    {machine.heartbeatStatus === 2 && (
                      <Tag color="#c6ea6c" className="font-medium rounded-sm bottom-0.5 !px-0.5">
                        <div className="!text-xs max-[450px]:!text-[10px] text-black">CMD</div>
                      </Tag>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-0.5 mb-1 justify-between">
                  <div className="flex flex-row justify-between items-center h-3">
                    <div style={{ width: '70%' }}>
                      <Progress
                        percent={parseFloat(
                          (100 - (100 * (machine.freeSpace || 0)) / (machine.totalSpace || 1)).toFixed(2),
                        )}
                        strokeWidth={window.innerWidth < 768 ? 8 : 12}
                        status="active"
                        strokeColor={(machine.freeSpace || 0) < 1024 ? '#ff3333' : '#1e90ff'}
                        showInfo={false}
                      />
                    </div>
                    <div
                      className={`ml-2 font-semibold ${machine.batteryVolt && machine.batteryVolt < 11.8 ? 'bg-yellow-400 text-black' : 'text-cyan-600'}`}
                    >
                      {machine.batteryVolt?.toFixed(2)}V
                    </div>
                  </div>

                  <div className="flex flex-row justify-between items-center text-cyan-600 font-bold">
                    <div>
                      {formatLastUpdate(machine.lastUpdate)} ({formatMinsAgo(machine.lastUpdatedMinsAgo)})
                    </div>
                    <div>
                      <div
                        className={
                          'font-bold ' +
                          (machine.status === 1
                            ? 'text-cyan-600'
                            : machine.status === 0
                              ? 'text-gray-500'
                              : 'font-medium text-gray-500')
                        }
                      >
                        {machine.status === 1
                          ? 'OK'
                          : machine.status === 0
                            ? 'OFF'
                            : 'SLEEP (' + dayjs(machine.wakeTime).format('HH:mm') + ')'}
                      </div>
                    </div>
                  </div>

                  <div
                    className={
                      'flex flex-row justify-between items-center font-bold ' +
                      (machine.lastCapturedMinsAgo != null && machine.lastCapturedMinsAgo <= 75
                        ? 'text-cyan-600'
                        : 'text-red-500')
                    }
                  >
                    <div>
                      {formatLastUpdate(machine.lastCapturedTime)} ({formatMinsAgo(machine.lastCapturedMinsAgo)})
                    </div>
                    <div
                      className={
                        machine.lastCapturedImage !== machine.lastUploadedImage
                          ? 'border-b-2 border-red-500 pb-0.5'
                          : ''
                      }
                    >
                      {machine.lastCapturedImage?.split('.')[0]}
                    </div>
                  </div>

                  <div
                    className={
                      'flex flex-row justify-between items-center font-bold ' +
                      (machine.lastUploadedMinsAgo != null && machine.lastUploadedMinsAgo <= 75
                        ? 'text-cyan-600'
                        : 'text-red-500')
                    }
                  >
                    <div>
                      {formatLastUpdate(machine.lastUploadedTime)} ({formatMinsAgo(machine.lastUploadedMinsAgo)})
                    </div>
                    <div>{machine.lastUploadedImage?.split('.')[0]}</div>
                  </div>
                  <div className={`flex flex-row justify-between items-center text-cyan-600 font-semibold text-[10px]`}>
                    <div>{formatVersionInfo(machine)}</div>
                    <div
                      className={` ${machine.uploadSpeed != null && machine.uploadSpeed < 60 ? 'bg-yellow-400 text-black' : ''}`}
                    >
                      {machine.uploadSpeed + 'KB/s'}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {filteredMachines.length === 0 && !machinesFacade.isLoading && (
            <div style={{ textAlign: 'center', marginTop: '3.125rem' }}>
              <Text type="secondary">No machines found</Text>
            </div>
          )}
        </PullToRefresh>
      </div>
    </>
  );
}
