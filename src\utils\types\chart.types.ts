/**
 * Chart Monitor Component Type Definitions
 * 
 * This file contains all TypeScript type definitions used by the chart monitoring
 * component and related functionality.
 */

// Chart data point interface
export interface ChartDataPoint {
  /** Measured value */
  value: number;
  /** Timestamp when the measurement was taken */
  createdOnDate: string | Date;
  /** Optional additional metadata */
  metadata?: Record<string, any>;
}

// Chart type enumeration
export type ChartType = 'battery' | 'uploadSpeed' | 'temperature' | 'humidity';

// Chart configuration interface
export interface ChartConfig {
  /** Chart type identifier */
  type: ChartType;
  /** Chart title */
  title: string;
  /** Y-axis unit label */
  unit: string;
  /** Chart color index */
  colorIndex: number;
  /** Chart container element ID */
  elementId: string;
  /** API endpoint suffix */
  apiEndpoint: string;
}

// Chart monitor component props
export interface ChartMonitorProps {
  /** Device name/identifier */
  deviceName?: string;
  /** Environment configuration */
  environment?: Environment;
  /** Custom chart configurations */
  chartConfigs?: ChartConfig[];
  /** Whether to show data tables */
  showDataTables?: boolean;
  /** Custom CSS class names */
  className?: string;
  /** Callback when component loads */
  onLoad?: () => void;
  /** Callback when error occurs */
  onError?: (error: Error) => void;
  /** Callback when data is updated */
  onDataUpdate?: (type: ChartType, data: ChartDataPoint[]) => void;
}

// Chart state interface
export interface ChartState {
  /** Chart data points */
  data: ChartDataPoint[];
  /** Number of data points to fetch */
  size: number;
  /** Whether data is currently loading */
  isLoading: boolean;
  /** Error message if any */
  error?: string;
  /** Last update timestamp */
  lastUpdate?: Date;
}

// Chart monitor state interface
export interface ChartMonitorState {
  /** Device name */
  deviceName: string;
  /** Overall loading state */
  isLoading: boolean;
  /** Chart states by type */
  charts: Record<ChartType, ChartState>;
  /** Error messages */
  errors: Record<ChartType, string | null>;
}

// Data table column interface
export interface DataTableColumn {
  /** Column key */
  key: string;
  /** Column title */
  title: string;
  /** Column width */
  width?: string;
  /** Custom render function */
  render?: (value: any, record: any, index: number) => React.ReactNode;
  /** Whether column is sortable */
  sortable?: boolean;
}

// Data table props interface
export interface DataTableProps {
  /** Table columns configuration */
  columns: DataTableColumn[];
  /** Table data */
  data: any[];
  /** Whether table is loading */
  loading?: boolean;
  /** Table row height */
  rowHeight?: string;
  /** Whether to show search */
  showSearch?: boolean;
  /** Whether to show pagination */
  showPagination?: boolean;
  /** Page size */
  pageSize?: number;
  /** Custom CSS class */
  className?: string;
}

// Chart service interface
export interface ChartService {
  /** Get battery time series data */
  getTimeBattery: (deviceName: string, count: number) => Promise<ApiResponse<ChartDataPoint[]>>;
  /** Get upload speed time series data */
  getTimeUploadSpeed: (deviceName: string, count: number) => Promise<ApiResponse<ChartDataPoint[]>>;
  /** Get temperature time series data */
  getTimeTemperature: (deviceName: string, count: number) => Promise<ApiResponse<ChartDataPoint[]>>;
  /** Get humidity time series data */
  getTimeHumidity: (deviceName: string, count: number) => Promise<ApiResponse<ChartDataPoint[]>>;
}

// API response interface
export interface ApiResponse<T> {
  /** Response data */
  data: T;
  /** Success status */
  success: boolean;
  /** Error message if any */
  message?: string;
  /** Response metadata */
  metadata?: Record<string, any>;
}

// Environment configuration interface
export interface Environment {
  /** Admin API base URL */
  adminApiUrl: string;
  /** Whether running in production */
  production: boolean;
  /** User data encryption key */
  userData: string;
  /** GPS URL */
  gpsUrl: string;
}

// ECharts option interface (simplified)
export interface EChartsOption {
  /** Chart colors */
  color: string[];
  /** Legend configuration */
  legend: {
    show: boolean;
  };
  /** Grid configuration */
  grid: {
    left: string;
    top: string;
    right: string;
    bottom: string;
    containLabel: boolean;
  };
  /** Tooltip configuration */
  tooltip: {
    trigger: string;
    formatter: (params: any) => string;
    extraCssText: string;
    axisPointer: any;
  };
  /** X-axis configuration */
  xAxis: {
    type: string;
    name: string;
    boundaryGap: boolean;
    axisLabel: any;
    nameTextStyle: any;
    axisLine: any;
    data: string[];
  };
  /** Y-axis configuration */
  yAxis: {
    type: string;
    name: string;
    scale: boolean;
    axisLabel: any;
    nameTextStyle: any;
    splitLine: any;
    axisLine: any;
    axisTick: any;
  };
  /** Series configuration */
  series: Array<{
    name: string;
    type: string;
    smooth: boolean;
    symbolSize: number;
    zlevel: number;
    itemStyle: any;
    areaStyle: any;
    data: number[];
  }>;
}

// Chart hook return type
export interface UseChartReturn {
  /** Chart state */
  state: ChartState;
  /** Load chart data */
  loadData: () => Promise<void>;
  /** Increase data size */
  increaseSize: (increment: number) => void;
  /** Reload chart data */
  reload: () => Promise<void>;
  /** Clear chart data */
  clear: () => void;
}

// Chart monitor hook return type
export interface UseChartMonitorReturn {
  /** Overall state */
  state: ChartMonitorState;
  /** Chart hooks by type */
  charts: Record<ChartType, UseChartReturn>;
  /** Set device name */
  setDeviceName: (name: string) => void;
  /** Reload all charts */
  reloadAll: () => Promise<void>;
  /** Clear all charts */
  clearAll: () => void;
}

// Chart utilities interface
export interface ChartUtils {
  /** Format date for display */
  formatDate: (date: string | Date) => string;
  /** Format chart data for ECharts */
  formatChartData: (data: ChartDataPoint[]) => {
    xAxisData: string[];
    yAxisData: number[];
  };
  /** Convert hex color to RGBA */
  hexToRgba: (hex: string, opacity: number) => string;
  /** Generate chart option */
  generateChartOption: (
    data: ChartDataPoint[],
    config: ChartConfig
  ) => EChartsOption;
}
