import { keyToken } from './variable';

export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem(keyToken);
  return !!token;
};

export const redirectToLogin = (): void => {
  window.location.href = '#/auth/login';
};

export const checkAuthAndRedirect = (): boolean => {
  if (!isAuthenticated()) {
    redirectToLogin();
    return false;
  }
  return true;
};

export const logout = (): void => {
  localStorage.removeItem(keyToken);
  localStorage.removeItem('15c665b7-592f-4b60-b31f-a252579a3bd0'); // keyRefreshToken
  localStorage.removeItem('m8nvn*&hKwcgb^D-D#Hz^5CXfKySpY'); // keyUser
  redirectToLogin();
};
