import { LockOutlined, MailOutlined, PhoneOutlined, UsergroupAddOutlined, UserOutlined } from '@ant-design/icons';
import { EStatusGlobal, GlobalFacade, RegisterModel } from '@store';
import { lang, routerLinks } from '@utils';
import { Button, Col, Flex, Form, Image, Input, Row, Typography } from 'antd';
import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { Link } from 'react-router-dom';
const Page = () => {
  const navigate = useNavigate();
  const globalFacade = GlobalFacade();
  const { status, register, isLoading } = globalFacade;
  const [registerForm] = Form.useForm();
  useEffect(() => {
    if (status === EStatusGlobal.registerFulfilled) {
      navigate('/' + lang + routerLinks('Login'), { replace: true });
    }
  }, [status]);

  const onFinish = (values: RegisterModel) => {
    if (values.phoneNumber) {
      if (!values.phoneNumber.startsWith('0')) {
        values.phoneNumber = '0' + values.phoneNumber;
      }
    }
    register(values);
  };

  return (
    <>
      <div className="!w-full md:!w-md">
        <Flex align="center" justify="center" gap={10} vertical>
          <Image preview={false} alt="logo-auth" src="/assets/images/logo.png" />
          <Typography.Title className="!mb-5" level={4}>
            Đăng ký tài khoản
          </Typography.Title>
        </Flex>
        <Form
          autoComplete="off"
          form={registerForm}
          initialValues={{
            role: 'CUSTOMER',
          }}
          onFinish={onFinish}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item<RegisterModel>
                name="name"
                rules={[
                  { required: true, message: 'Họ tên là bắt buộc!' },
                  {
                    pattern: /^[a-zA-ZÀ-ỹ\s]+$/u,
                    message: 'Họ và tên chỉ được chứa chữ cái và khoảng trắng, không chứa số hoặc ký tự đặc biệt!',
                  },
                ]}
              >
                <Input size="large" prefix={<UserOutlined />} placeholder="Họ và tên" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={24} md={12}>
              <Form.Item<RegisterModel> name="email" rules={[{ type: 'email', message: 'E-mail không hợp lệ!' }]}>
                <Input size="large" prefix={<MailOutlined />} placeholder="E-mail" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={24} md={12}>
              <Form.Item<RegisterModel>
                name="phoneNumber"
                rules={[
                  { required: true, message: 'Số điện thoại là bắt buộc!' },
                  {
                    pattern: /^[0-9]{9,10}$/,
                    message: 'Số điện thoại không đúng định dạng',
                  },
                ]}
              >
                <Input
                  size="large"
                  addonBefore="+84"
                  prefix={<PhoneOutlined />}
                  placeholder="Số điện thoại"
                  onBlur={(e) => {
                    const value = e.target.value;
                    if (value.startsWith('0')) {
                      registerForm.setFieldsValue({ phoneNumber: value.substring(1) });
                    }
                  }}
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={24} md={12}>
              <Form.Item<RegisterModel>
                name="password"
                rules={[
                  { required: true, message: 'Mật khẩu là bắt buộc!' },
                  {
                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/,
                    message: 'Mật khẩu cần có ít nhất 8 ký tự, gồm chữ hoa, chữ thường, số và ký tự đặc biệt!',
                  },
                ]}
                hasFeedback
              >
                <Input.Password size="large" prefix={<LockOutlined />} placeholder="Mật khẩu" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={24} md={12}>
              <Form.Item<RegisterModel>
                name="confirmPassword"
                dependencies={['password']}
                rules={[
                  { required: true, message: 'Xác nhận mật khẩu là bắt buộc!' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('Mật khẩu không khớp!'));
                    },
                  }),
                ]}
                hasFeedback
              >
                <Input.Password size="large" prefix={<LockOutlined />} placeholder="Xác nhận mật khẩu" />
              </Form.Item>
            </Col>

            <Col span={24} hidden>
              <Form.Item<RegisterModel>
                name="role"
                rules={[{ required: true, message: 'Loại người dùng là bắt buộc!' }]}
              >
                <Input size="large" prefix={<UsergroupAddOutlined />} placeholder="Loại người dùng" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item className="!mb-2">
                <Button loading={isLoading} size="large" block type="primary" htmlType="submit">
                  Đăng ký
                </Button>
              </Form.Item>
              <Form.Item>
                Bạn đã có tài khoản?{' '}
                <Link to={`${routerLinks('Login')}`} className="!text-primary-gold !underline">
                  Đăng nhập
                </Link>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    </>
  );
};

export default Page;
